<?php
// Include PSR-4 autoloader
require_once __DIR__ . '/../autoload.php';

use Frontend\Config\FrontendConfig;
use Frontend\Services\SessionService;

// Initialize PSR-4 configuration and session
FrontendConfig::init();
SessionService::init();

// Check if user is logged in
if (!SessionService::isAuthenticated()) {
    header('Location: ../index.php');
    exit;
}

$user = SessionService::getCurrentUser();

// Set variables for header
$pageTitle = 'Investment Dashboard - TLS Wallet';
$currentPage = 'invest';
$basePath = '.';
$cssPath = 'css';
$additionalCSS = ['css/investment-redesign.css'];

// Include header
include '../includes/header.php';
?>

            <!-- Modern Investment Dashboard -->
            <div class="investment-page-redesign">
                <!-- Smart Header with Integrated Controls -->
                <div class="smart-header">
                    <div class="header-main">
                        <div class="page-title">
                            <h1>📊 Investment Dashboard</h1>
                            <p>Manage your USDT investments and track returns</p>
                        </div>
                        <div class="balance-widget">
                            <div class="balance-container">
                                <span class="balance-label">Available Balance</span>
                                <div class="balance-value">
                                    <span id="availableBalance" class="amount">Loading...</span>
                                    <span class="currency">USDT</span>
                                </div>
                                <div class="balance-status" id="balanceStatus">
                                    <span class="status-indicator"></span>
                                    <span class="status-text">Ready to invest</span>
                                </div>
                            </div>
                            <div class="balance-actions">
                                <button class="balance-btn primary" onclick="window.location.href='make_investment.php'">
                                    <span class="btn-icon">🚀</span>
                                    <span class="btn-text">New Investment</span>
                                </button>
                                <button class="balance-btn secondary" onclick="window.location.href='deposit.php'">
                                    <span class="btn-icon">💰</span>
                                    <span class="btn-text">Add Funds</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Dashboard Stats Grid -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">�</div>
                        <div class="stat-info">
                            <span class="stat-label">Active Investments</span>
                            <span class="stat-value" id="activeInvestmentsCount">0</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">�</div>
                        <div class="stat-info">
                            <span class="stat-label">Total Earned</span>
                            <span class="stat-value" id="totalEarnings">0.00 USDT</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">⚡</div>
                        <div class="stat-info">
                            <span class="stat-label">Daily Returns</span>
                            <span class="stat-value" id="dailyReturns">0.00 USDT</span>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon">📊</div>
                        <div class="stat-info">
                            <span class="stat-label">Success Rate</span>
                            <span class="stat-value" id="successRate">100%</span>
                        </div>
                    </div>
                </div>

                <!-- Main Content Layout -->
                <div class="content-layout">
                    <!-- Primary Content -->
                    <div class="content-primary">
                        <!-- Active Investments Section -->
                        <div class="content-section">
                            <div class="section-header">
                                <div class="section-title">
                                    <h2>🔥 Active Investments</h2>
                                    <span class="section-count" id="activeCount">0 active</span>
                                </div>
                                <div class="section-actions">
                                    <button class="action-btn-sm" onclick="refreshActiveInvestments()" title="Refresh">
                                        🔄
                                    </button>
                                    <button class="action-btn-sm" onclick="window.location.href='make_investment.php'" title="New Investment">
                                        ➕
                                    </button>
                                </div>
                            </div>
                            <div class="section-content">
                                <div id="activeInvestmentsList" class="investments-grid">
                                    <div class="loading-state">
                                        <div class="loading-spinner"></div>
                                        <span>Loading active investments...</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Investment History Section -->
                        <div class="content-section">
                            <div class="section-header">
                                <div class="section-title">
                                    <h2>📜 Investment History</h2>
                                    <span class="section-count" id="historyCount">Recent activity</span>
                                </div>
                                <div class="section-actions">
                                    <button class="action-btn-sm" onclick="refreshInvestmentHistory()" title="Refresh">
                                        🔄
                                    </button>
                                    <button class="action-btn-sm" onclick="window.location.href='transactions.php'" title="View All">
                                        📊
                                    </button>
                                </div>
                            </div>
                            <div class="section-content">
                                <div id="investmentHistoryList" class="history-grid">
                                    <div class="loading-state">
                                        <div class="loading-spinner"></div>
                                        <span>Loading investment history...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Balance Based Container (Hidden, for compatibility) -->
                <div id="balanceBasedContainer" style="display: none;">
                    <!-- Content will be dynamically loaded based on balance -->
                </div>
            </div>

<?php 
// Include footer
include '../includes/footer.php';
?>
    <script src="js/csrf-manager.js"></script>
    <script src="js/invest.js"></script>
</body>
</html>
