// Investment Page JavaScript - SECURITY HARDENED VERSION
let securityUtils = null;
let csrfManager = null;

// Load security utilities first
async function loadSecurityUtils() {
    if (window.SecurityUtils) {
        securityUtils = window.SecurityUtils;
        return;
    }
    
    try {
        await new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'js/security-utils.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
        securityUtils = window.SecurityUtils;
    } catch (error) {
        console.error('Failed to load security utilities:', error);
        // Fallback security function
        securityUtils = {
            escapeHtml: (str) => String(str).replace(/[&<>"']/g, (match) => ({
                '&': '&amp;', '<': '&lt;', '>': '&gt;', '"': '&quot;', "'": '&#39;'
            })[match])
        };
    }
}

document.addEventListener('DOMContentLoaded', async function() {
    await loadSecurityUtils();
    
    // Initialize CSRF manager
    if (typeof CSRFManager !== 'undefined') {
        csrfManager = new CSRFManager();
        csrfManager.setContext('user');
    }
    
    initInvestPage();
});

let userBalance = 0;
let selectedPlan = 'basic';
let investmentPlans = {}; // Will be loaded from API
let systemConfig = null; // Will store system configuration

function initInvestPage() {
    loadSystemConfiguration();
    loadInvestmentPlans();
    loadUserBalance();
    loadActiveInvestments();
    loadInvestmentHistory();
    loadDashboardStats();
    loadInvestmentPlansPreview();
    initFooterMenu();
}

function setupInvestmentForm() {
    const investForm = document.getElementById('investmentForm');
    const calculateBtn = document.getElementById('calculateBtn');
    const investAmountInput = document.getElementById('investAmount');
    const planCards = document.querySelectorAll('.plan-card');

    // Only setup plan selection if plan cards exist
    if (planCards.length > 0) {
        // Plan selection
        planCards.forEach(card => {
            card.addEventListener('click', function() {
                // Remove active class from all cards
                planCards.forEach(c => c.classList.remove('active'));
                // Add active class to clicked card
                this.classList.add('active');
                
                selectedPlan = this.dataset.plan;
                const selectedPlanInput = document.getElementById('selectedPlan');
                if (selectedPlanInput) {
                    selectedPlanInput.value = selectedPlan;
                }
                  // Update minimum amount
                const minAmount = investmentPlans[selectedPlan].minAmount;
                if (investAmountInput) {
                    investAmountInput.min = minAmount;
                    investAmountInput.placeholder = `Enter amount (minimum ${securityUtils.escapeHtml(minAmount)} USDT)`;
                }
                
                // Clear previous calculations
                const investmentSummary = document.getElementById('investmentSummary');
                const investBtn = document.getElementById('investBtn');
                if (investmentSummary) investmentSummary.style.display = 'none';
                if (investBtn) investBtn.disabled = true;
            });
        });

        // Set default selected plan (only if basic plan card exists)
        const basicPlanCard = document.querySelector('[data-plan="basic"]');
        if (basicPlanCard) {
            basicPlanCard.classList.add('active');
        }
    }

    // Calculate button
    if (calculateBtn) {
        calculateBtn.addEventListener('click', calculateReturns);
    }

    // Form submission
    if (investForm) {
        investForm.addEventListener('submit', handleInvestment);
    }

    // Amount input validation
    if (investAmountInput) {
        investAmountInput.addEventListener('input', function() {
            const investmentSummary = document.getElementById('investmentSummary');
            const investBtn = document.getElementById('investBtn');
            if (investmentSummary) investmentSummary.style.display = 'none';
            if (investBtn) investBtn.disabled = true;
        });
    }
}

async function loadUserBalance() {
    try {
        const response = await apiCall('get_balance');
        
        // Handle successful response or when balance is available
        if (response.success || (response.balance !== undefined && !response.error)) {
            userBalance = parseFloat(response.balance) || 0;
            const balanceElement = document.getElementById('availableBalance');
            if (balanceElement) {
                balanceElement.textContent = userBalance.toFixed(2);
            }

            // Update balance status
            updateBalanceStatus();

            // Update balance-based container content
            updateBalanceBasedContent();
        } else if (response.error === "Main wallet not found") {
            // This is expected for new users who don't have a wallet yet
            // Treat as zero balance
            userBalance = 0;
            const balanceElement = document.getElementById('availableBalance');
            if (balanceElement) {
                balanceElement.textContent = '0.00';
            }
            
            // Update balance-based container content for new user
            updateBalanceBasedContent();
        } else {
            console.error('Error loading balance:', response.message || response.error);
            const balanceElement = document.getElementById('availableBalance');
            if (balanceElement) balanceElement.textContent = 'Error';
            
            // Show error message
            showBalanceError();
        }
    } catch (error) {
        console.error('Error loading balance:', error);
        const balanceElement = document.getElementById('availableBalance');
        if (balanceElement) balanceElement.textContent = 'Error';
        
        // Show error message
        showBalanceError();
    }
}

// Update balance-based container content
function updateBalanceBasedContent() {
    const balanceBasedContainer = document.getElementById('balanceBasedContainer');
    const insufficientMsg = document.getElementById('insufficientBalanceMsg');
    const investForm = document.getElementById('investmentForm');
    
    if (!balanceBasedContainer) return;
    
    if (userBalance < 300) {
        // Determine if user is completely new (0 balance) or just has insufficient funds
        const isNewUser = userBalance === 0;
        const title = isNewUser ? "Welcome! Ready to Start Investing?" : "Insufficient Balance";
        const subtitle = isNewUser ? 
            "You're just one step away from earning daily returns on your USDT!" :
            `Your current balance is <strong>${securityUtils.escapeHtml(userBalance.toFixed(2))} USDT</strong>`;
        
        // Show insufficient balance message with appropriate messaging
        balanceBasedContainer.innerHTML = `
            <div class="insufficient-balance-container">
                <div class="warning-card">
                    <div class="warning-icon">
                        ${isNewUser ? `
                            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#28a745" stroke-width="2">
                                <circle cx="12" cy="12" r="10"></circle>
                                <path d="M9,12l2,2 4,-4"></path>
                            </svg>
                        ` : `
                            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#ffc107" stroke-width="2">
                                <circle cx="12" cy="12" r="10"></circle>
                                <line x1="12" y1="8" x2="12" y2="12"></line>
                                <line x1="12" y1="16" x2="12.01" y2="16"></line>
                            </svg>
                        `}
                    </div>
                    <h4>${title}</h4>
                    <p>${subtitle}</p>
                    <p>You need at least <strong>300 USDT</strong> to start investing and earn daily returns.</p>
                    ${!isNewUser ? `
                        <div class="balance-needed">
                            <span class="needed-amount">Amount needed: <strong>${securityUtils.escapeHtml((300 - userBalance).toFixed(2))} USDT</strong></span>
                        </div>
                    ` : ''}
                    <div class="investment-benefits">
                        <h5>Why Invest with Us?</h5>
                        <ul>
                            <li>Daily returns on your investment</li>
                            <li>Secure USDT-based transactions</li>
                            <li>Transparent investment tracking</li>
                            <li>Flexible investment plans</li>
                        </ul>
                    </div>
                    <div class="warning-actions">
                        <a href="deposit.php" class="btn btn-primary">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="12" y1="5" x2="12" y2="19"></line>
                                <line x1="5" y1="12" x2="19" y2="12"></line>
                            </svg>
                            ${isNewUser ? 'Make Your First Deposit' : 'Add Funds'}
                        </a>
                        <a href="dashboard.php" class="btn btn-secondary">Back to Dashboard</a>
                    </div>
                </div>
            </div>
        `;
        
        // Hide the separate insufficient message and form
        if (insufficientMsg) insufficientMsg.style.display = 'none';
        if (investForm) investForm.style.display = 'none';
    } else {
        // Show investment form
        balanceBasedContainer.innerHTML = `
            <div class="investment-available-container">
                <div class="balance-status-card">
                    <div class="status-icon">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="#28a745" stroke-width="2">
                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                            <polyline points="22,4 12,14.01 9,11.01"></polyline>
                        </svg>
                    </div>
                    <div class="status-content">
                        <h4>Ready to Invest</h4>
                        <p>Your balance: <strong>${securityUtils.escapeHtml(userBalance.toFixed(2))} USDT</strong></p>
                        <p>You can start investing now!</p>
                    </div>
                </div>
                <div class="investment-actions">
                    <a href="make_investment.php" class="btn btn-primary">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="12" y1="5" x2="12" y2="19"></line>
                            <line x1="5" y1="12" x2="19" y2="12"></line>
                        </svg>
                        Start Investment
                    </a>
                    <a href="deposit.php" class="btn btn-outline">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="12" y1="5" x2="12" y2="19"></line>
                            <line x1="5" y1="12" x2="19" y2="12"></line>
                        </svg>
                        Add More Funds
                    </a>
                </div>
            </div>
        `;
        
        // Hide the separate insufficient message and show form
        if (insufficientMsg) insufficientMsg.style.display = 'none';
        if (investForm) investForm.style.display = 'block';
    }
}

function calculateReturns() {
    const amountInput = document.getElementById('investAmount');
    if (!amountInput) return;
      const amount = parseFloat(amountInput.value);
    
    if (!amount || amount < investmentPlans[selectedPlan].minAmount) {
        showMessage(`Minimum investment for ${securityUtils.escapeHtml(investmentPlans[selectedPlan].name)} is ${securityUtils.escapeHtml(investmentPlans[selectedPlan].minAmount)} USDT`, 'error');
        return;
    }
    
    if (amount > userBalance) {
        showMessage('Insufficient balance for this investment amount', 'error');
        return;
    }
    
    const plan = investmentPlans[selectedPlan];
    const dailyReturn = amount * plan.dailyRate;
    const totalReturn = dailyReturn * plan.duration;
      // Update summary
    const summaryAmount = document.getElementById('summaryAmount');
    const summaryDailyReturn = document.getElementById('summaryDailyReturn');
    const summaryTotalReturn = document.getElementById('summaryTotalReturn');
    
    if (summaryAmount) summaryAmount.textContent = `${amount.toFixed(2)} USDT`;
    if (summaryDailyReturn) summaryDailyReturn.textContent = `${dailyReturn.toFixed(2)} USDT`;
    if (summaryTotalReturn) summaryTotalReturn.textContent = `${totalReturn.toFixed(2)} USDT`;
    
    const investmentSummary = document.getElementById('investmentSummary');
    const investBtn = document.getElementById('investBtn');
    if (investmentSummary) investmentSummary.style.display = 'block';
    if (investBtn) investBtn.disabled = false;
}

async function handleInvestment(e) {
    e.preventDefault();
    
    const investAmountInput = document.getElementById('investAmount');
    if (!investAmountInput) return;
      const amount = parseFloat(investAmountInput.value);
    
    if (!amount || amount < investmentPlans[selectedPlan].minAmount) {
        showMessage(`Minimum investment for ${securityUtils.escapeHtml(investmentPlans[selectedPlan].name)} is ${securityUtils.escapeHtml(investmentPlans[selectedPlan].minAmount)} USDT`, 'error');
        return;
    }
    
    if (amount > userBalance) {
        showMessage('Insufficient balance for this investment amount', 'error');
        return;
    }
    
    // Disable button to prevent double submission
    const investBtn = document.getElementById('investBtn');
    if (!investBtn) return;
    
    const originalText = investBtn.textContent;
    investBtn.disabled = true;
    investBtn.textContent = 'Processing...';
    
    try {
        const response = await apiCall('create_investment', {
            amount: amount,
            plan: selectedPlan
        });
          if (response.success) {
            showMessage('Investment created successfully!', 'success');
            
            // Reset form
            const investmentForm = document.getElementById('investmentForm');
            const investmentSummary = document.getElementById('investmentSummary');
            const basicPlanCard = document.querySelector('[data-plan="basic"]');
            const otherPlanCards = document.querySelectorAll('.plan-card:not([data-plan="basic"])');
            
            if (investmentForm) investmentForm.reset();
            if (investmentSummary) investmentSummary.style.display = 'none';
            if (basicPlanCard) basicPlanCard.classList.add('active');
            if (otherPlanCards.length > 0) {
                otherPlanCards.forEach(card => {
                    card.classList.remove('active');
                });
            }
            
            // Reload data
            loadUserBalance();
            loadActiveInvestments();
            loadInvestmentHistory();
        } else {
            showMessage(response.message || 'Failed to create investment', 'error');
        }
    } catch (error) {
        console.error('Error creating investment:', error);
        showMessage('Network error. Please try again.', 'error');
    } finally {
        investBtn.disabled = false;
        investBtn.textContent = originalText;
    }
}

async function loadActiveInvestments() {
    try {
        const response = await apiCall('get_active_investments');
        const container = document.getElementById('activeInvestmentsList');
        
        if (!container) return;

        if (response.success && response.investments && response.investments.length > 0) {
            // Update active count
            const activeCountElement = document.getElementById('activeCount');
            if (activeCountElement) {
                activeCountElement.textContent = `${response.investments.length} active`;
            }

            container.innerHTML = response.investments.map(investment => `
                <div class="investment-item" data-investment-id="${securityUtils.escapeHtml(investment.id)}">
                    <div class="investment-header">
                        <span class="investment-plan">${securityUtils.escapeHtml(investment.plan_name)}</span>
                        <span class="investment-amount">${securityUtils.escapeHtml(parseFloat(investment.amount).toFixed(2))} USDT</span>
                    </div>
                    <div class="investment-progress">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: ${Math.round((investment.days_elapsed / 20) * 100)}%"></div>
                        </div>
                        <div class="progress-text">
                            ${securityUtils.escapeHtml(investment.days_elapsed)} of 20 days completed
                        </div>
                    </div>
                    <div class="investment-earnings">
                        <span class="daily-return">+${securityUtils.escapeHtml(parseFloat(investment.daily_return).toFixed(2))} USDT/day</span>
                        <span class="total-earned">Total: ${securityUtils.escapeHtml(parseFloat(investment.total_earned).toFixed(2))} USDT</span>
                    </div>
                </div>
            `).join('');
            
            // Add click event listeners to investment items
            container.addEventListener('click', (e) => {
                const investmentItem = e.target.closest('.investment-item');
                if (investmentItem) {
                    const investmentId = investmentItem.dataset.investmentId;
                    if (investmentId) {
                        // Add click animation
                        investmentItem.style.transform = 'scale(0.98)';
                        setTimeout(() => {
                            investmentItem.style.transform = '';
                            // Navigate to active plan page
                            window.location.href = `active_plan.php?id=${securityUtils.escapeHtml(investmentId)}`;
                        }, 150);
                    }
                }
            });
        } else {
            // Update active count
            const activeCountElement = document.getElementById('activeCount');
            if (activeCountElement) {
                activeCountElement.textContent = '0 active';
            }

            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📈</div>
                    <h4 class="empty-title">No Active Investments</h4>
                    <p class="empty-description">Start your investment journey by choosing a plan that fits your goals.</p>
                    <a href="make_investment.php" class="empty-action">
                        <span>🚀</span>
                        <span>Start Investing</span>
                    </a>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading active investments:', error);
        if (container) {
            container.innerHTML = '<div class="error">Error loading investments</div>';
        }
    }
}

async function loadInvestmentHistory() {
    try {
        const response = await apiCall('get_investment_history');
        const container = document.getElementById('investmentHistoryList');
        
        if (!container) return;
          if (response.success && response.history && response.history.data && response.history.data.length > 0) {
            // Update history count
            const historyCountElement = document.getElementById('historyCount');
            if (historyCountElement) {
                historyCountElement.textContent = `${response.history.data.length} transactions`;
            }

            // Show only recent 5 items for compact view
            const recentHistory = response.history.data.slice(0, 5);

            container.innerHTML = recentHistory.map(investment => `
                <div class="history-item">
                    <div class="history-header">
                        <span class="history-type">${securityUtils.escapeHtml(investment.plan_name)}</span>
                        <span class="history-date">${new Date(investment.created_at).toLocaleDateString()}</span>
                    </div>
                    <div class="history-details">
                        <span class="history-amount">${securityUtils.escapeHtml(parseFloat(investment.amount).toFixed(2))} USDT</span>
                        <span class="history-status ${securityUtils.escapeHtml(investment.status)}">${securityUtils.escapeHtml(investment.status.toUpperCase())}</span>
                    </div>
                </div>
            `).join('') + (response.history.data.length > 5 ? `
                <div class="history-item" style="text-align: center; cursor: pointer;" onclick="window.location.href='transactions.php'">
                    <div style="display: flex; align-items: center; justify-content: center; gap: 0.5rem; font-weight: 600; color: var(--primary-color);">
                        <span>View All History</span>
                        <span>→</span>
                    </div>
                </div>
            ` : '');
                                    <td class="date-cell">
                                        <div class="date-main">${securityUtils.escapeHtml(new Date(investment.created_at).toLocaleDateString())}</div>
                                        <div class="date-time">${securityUtils.escapeHtml(new Date(investment.created_at).toLocaleTimeString([], {hour: '2-digit', minute: '2-digit'}))}</div>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
            
            // Add scroll detection for better UX
            const tableResponsive = container.querySelector('.table-responsive');
            if (tableResponsive) {
                // Add investment-history-card class for styling
                const card = container.closest('.card');
                if (card) {
                    card.classList.add('investment-history-card');
                }
                
                // Handle scroll indicators
                function updateScrollIndicators() {
                    const scrollLeft = tableResponsive.scrollLeft;
                    const scrollWidth = tableResponsive.scrollWidth;
                    const clientWidth = tableResponsive.clientWidth;
                    const maxScroll = scrollWidth - clientWidth;
                    
                    // Remove existing classes
                    tableResponsive.classList.remove('scrolled-left', 'scrolled-right');
                    
                    // Add appropriate classes based on scroll position
                    if (scrollLeft > 5) {
                        tableResponsive.classList.add('scrolled-left');
                    }
                    if (scrollLeft < maxScroll - 5) {
                        tableResponsive.classList.add('scrolled-right');
                    }
                }
                
                // Add scroll event listener
                tableResponsive.addEventListener('scroll', function(e) {
                    updateScrollIndicators();
                    // Hide scroll hint after user starts scrolling
                    e.target.classList.add('user-scrolled');
                });
                
                // Initial check
                setTimeout(updateScrollIndicators, 100);
                
                // Update on window resize
                window.addEventListener('resize', updateScrollIndicators);
                
                // Add mobile touch scrolling enhancement
                if (window.innerWidth <= 768) {
                    tableResponsive.style.webkitOverflowScrolling = 'touch';
                    tableResponsive.setAttribute('tabindex', '0');
                    tableResponsive.setAttribute('aria-label', 'Investment history table - scroll horizontally to see more data');
                }
            }
        } else {
            // Update history count
            const historyCountElement = document.getElementById('historyCount');
            if (historyCountElement) {
                historyCountElement.textContent = 'No history';
            }

            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">📜</div>
                    <h4 class="empty-title">No Investment History</h4>
                    <p class="empty-description">Your investment history will appear here once you start investing.</p>
                    <a href="make_investment.php" class="empty-action">
                        <span>🚀</span>
                        <span>Start Investing</span>
                    </a>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading investment history:', error);
        const container = document.getElementById('investmentHistoryList');
        if (container) {
            container.innerHTML = '<div class="error-message">Error loading investment history</div>';
        }
    }
}

async function loadSystemConfiguration() {
    try {
        const response = await apiCall('get_system_configuration');
        if (response.success) {
            systemConfig = response.data;
        } else {
            console.error('Failed to load system configuration:', response.message);
        }
    } catch (error) {
        console.error('Error loading system configuration:', error);
    }
}

async function loadInvestmentPlans() {
    try {
        const response = await apiCall('get_investment_plans');
        
        if (response.success) {
            const plans = response.plans;
            
            // Convert plans array to object for easy access
            investmentPlans = {};
            plans.forEach(plan => {
                investmentPlans[plan.plan_code] = {
                    id: plan.id,
                    name: plan.plan_name,
                    dailyRate: plan.daily_rate,
                    duration: plan.duration,
                    minAmount: plan.min_amount,
                    maxAmount: plan.max_amount,
                    description: plan.description,
                    features: plan.features || [],
                    isActive: plan.is_active,
                    isFeatured: plan.is_featured
                };
            });
            
            setupInvestmentForm();
        } else {
            console.error('Failed to load investment plans:', response.message);
            // Fallback to basic plan only
            useDefaultPlans();
        }
    } catch (error) {
        console.error('Error loading investment plans:', error);
        useDefaultPlans();
    }
}

function useDefaultPlans() {
    investmentPlans = {
        basic: {
            name: 'Basic Plan',
            dailyRate: 0.0625,
            duration: 20,
            minAmount: 300
        }
    };
    setupInvestmentForm();
}

// API call function with CSRF protection
async function apiCall(endpoint, data = {}) {
    // Define state-changing operations that require CSRF protection
    const stateChangingActions = [
        'create_investment', 'withdraw', 'record_deposit', 'change_password'
    ];
    
    // Use CSRF manager for state-changing operations
    if (csrfManager && stateChangingActions.includes(endpoint)) {
        try {
            return await csrfManager.secureRequest(endpoint, data);
        } catch (error) {
            console.error('CSRF secure request failed:', error);
            // Fallback to regular request but show error
            showMessage('Security token error. Please refresh the page and try again.', 'error');
            throw error;
        }
    }
    
    // Regular API call for read-only operations
    try {
        const response = await fetch('../ajax.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: endpoint,
                ...data
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error('API call failed:', error);
        throw error;
    }
}

// Message display function
function showMessage(message, type = 'info') {
    const messageDiv = document.getElementById('message');
    if (messageDiv) {
        messageDiv.textContent = message;
        messageDiv.className = `message ${type}`;
        messageDiv.style.display = 'block';
        
        setTimeout(() => {
            messageDiv.style.display = 'none';
        }, 5000);
    }
}

// Footer menu initialization
function initFooterMenu() {
    // Footer menu is already initialized by the main layout
}

function showBalanceError() {
    const balanceBasedContainer = document.getElementById('balanceBasedContainer');
    
    if (!balanceBasedContainer) return;
    
    balanceBasedContainer.innerHTML = `
        <div class="error-container">
            <div class="error-card">
                <div class="error-icon">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#dc3545" stroke-width="2">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="15" y1="9" x2="9" y2="15"></line>
                        <line x1="9" y1="9" x2="15" y2="15"></line>
                    </svg>
                </div>
                <h4>Unable to Load Balance</h4>
                <p>We couldn't load your account balance. This might be a temporary connection issue.</p>
                <div class="error-suggestions">
                    <h5>What you can do:</h5>
                    <ul>
                        <li>Check your internet connection</li>
                        <li>Try refreshing the page</li>
                        <li>If the problem persists, contact support</li>
                    </ul>
                </div>
                <div class="error-actions">
                    <button onclick="window.location.reload()" class="btn btn-primary">Refresh Page</button>
                    <a href="dashboard.php" class="btn btn-secondary">Back to Dashboard</a>
                    <a href="deposit.php" class="btn btn-outline">Continue to Deposit</a>
                </div>
            </div>
        </div>
    `;
}

// New Dashboard Functions

// Update balance status indicator
function updateBalanceStatus() {
    const statusElement = document.getElementById('balanceStatus');
    const statusIndicator = statusElement?.querySelector('.status-indicator');
    const statusText = statusElement?.querySelector('.status-text');

    if (statusElement && statusIndicator && statusText) {
        if (userBalance >= 300) {
            statusIndicator.style.background = '#10b981';
            statusText.textContent = 'Ready to invest';
        } else if (userBalance > 0) {
            statusIndicator.style.background = '#f59e0b';
            statusText.textContent = 'Add more funds';
        } else {
            statusIndicator.style.background = '#ef4444';
            statusText.textContent = 'No funds';
        }
    }
}

// Load dashboard statistics
async function loadDashboardStats() {
    try {
        // Load active investments count
        const activeResponse = await apiCall('get_active_investments');
        if (activeResponse.success && activeResponse.investments) {
            const activeCount = activeResponse.investments.length;
            const activeCountElement = document.getElementById('activeInvestmentsCount');
            if (activeCountElement) {
                activeCountElement.textContent = activeCount;
            }

            // Calculate total earnings and daily returns
            let totalEarnings = 0;
            let dailyReturns = 0;

            activeResponse.investments.forEach(investment => {
                totalEarnings += parseFloat(investment.total_earned || 0);
                dailyReturns += parseFloat(investment.daily_return || 0);
            });

            const totalEarningsElement = document.getElementById('totalEarnings');
            const dailyReturnsElement = document.getElementById('dailyReturns');

            if (totalEarningsElement) {
                totalEarningsElement.textContent = totalEarnings.toFixed(2);
            }
            if (dailyReturnsElement) {
                dailyReturnsElement.textContent = dailyReturns.toFixed(2);
            }
        }

        // Load performance metrics
        loadPerformanceMetrics();

    } catch (error) {
        console.error('Error loading dashboard stats:', error);
    }
}

// Load performance metrics
async function loadPerformanceMetrics() {
    try {
        // This would typically come from an API endpoint
        // For now, we'll calculate from available data
        const weeklyElement = document.getElementById('weeklyEarnings');
        const monthlyElement = document.getElementById('monthlyEarnings');
        const successRateElement = document.getElementById('successRate');

        if (weeklyElement) weeklyElement.textContent = '0.00 USDT';
        if (monthlyElement) monthlyElement.textContent = '0.00 USDT';
        if (successRateElement) successRateElement.textContent = '100%';

    } catch (error) {
        console.error('Error loading performance metrics:', error);
    }
}

// Load investment plans preview
async function loadInvestmentPlansPreview() {
    try {
        const response = await apiCall('get_investment_plans');
        if (response.success && response.plans) {
            renderInvestmentPlansPreview(response.plans);
        }
    } catch (error) {
        console.error('Error loading investment plans preview:', error);
        showPlansPreviewError();
    }
}

// Render investment plans preview
function renderInvestmentPlansPreview(plans) {
    const container = document.getElementById('investmentPlansPreview');
    if (!container) return;

    container.innerHTML = '';

    // Show only first 3 plans
    const previewPlans = plans.slice(0, 3);

    previewPlans.forEach(plan => {
        const planElement = document.createElement('div');
        planElement.className = 'plan-preview-item';
        planElement.onclick = () => window.location.href = 'make_investment.php';

        planElement.innerHTML = `
            <div class="plan-preview-header">
                <span class="plan-preview-name">${securityUtils.escapeHtml(plan.plan_name)}</span>
                <span class="plan-preview-rate">${(plan.daily_rate * 100).toFixed(2)}%</span>
            </div>
            <div class="plan-preview-details">
                <span>${plan.duration} days</span>
                <span>Min: ${plan.min_amount} USDT</span>
            </div>
        `;

        container.appendChild(planElement);
    });

    // Add "View All" link if there are more plans
    if (plans.length > 3) {
        const viewAllElement = document.createElement('div');
        viewAllElement.className = 'plan-preview-item';
        viewAllElement.style.textAlign = 'center';
        viewAllElement.style.fontWeight = '600';
        viewAllElement.style.color = 'var(--primary-color)';
        viewAllElement.onclick = () => window.location.href = 'make_investment.php';
        viewAllElement.innerHTML = `
            <div style="display: flex; align-items: center; justify-content: center; gap: 0.5rem;">
                <span>View All Plans</span>
                <span>→</span>
            </div>
        `;
        container.appendChild(viewAllElement);
    }
}

// Show plans preview error
function showPlansPreviewError() {
    const container = document.getElementById('investmentPlansPreview');
    if (!container) return;

    container.innerHTML = `
        <div class="empty-state">
            <div class="empty-icon">📋</div>
            <p class="empty-description">Unable to load investment plans</p>
        </div>
    `;
}

// Refresh functions for widget buttons
function refreshActiveInvestments() {
    loadActiveInvestments();
    loadDashboardStats();
}

function refreshInvestmentHistory() {
    loadInvestmentHistory();
}
