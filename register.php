<?php
require_once __DIR__ . '/autoload.php';

use Frontend\Config\FrontendConfig;
use Frontend\Services\SessionService;

// Initialize configuration and session
FrontendConfig::init();
SessionService::init();

// Redirect if already logged in
if (SessionService::isAuthenticated()) {
    if (SessionService::isAdmin()) {
        header('Location: admin.php');
    } else {
        header('Location: user/dashboard.php');
    }
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - TLS Wallet</title>
    <link rel="stylesheet" href="css/modern-auth.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="auth-wrapper">
        <!-- Background Elements -->
        <div class="bg-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
        </div>
        
        <!-- Main Content -->
        <div class="auth-container">
            <!-- Header Section -->
            <div class="auth-header">
                <div class="logo-container">
                    <div class="logo-icon">
                        <i class="fas fa-wallet"></i>
                    </div>
                    <h1>TLS Wallet</h1>
                </div>
                <p class="subtitle">Secure TRON & USDT Management</p>
            </div>

            <!-- Registration Form -->
            <div class="auth-card compact">
                <div class="card-header">
                    <h2>Create Account</h2>
                    <p>Join TLS Wallet for secure crypto management</p>
                </div>

                <form id="registerForm" class="auth-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="email">Email Address</label>
                            <div class="input-wrapper">
                                <i class="fas fa-envelope input-icon"></i>
                                <input type="email" id="email" name="email" required autocomplete="email">
                                <div class="input-focus-border"></div>
                            </div>
                            <div class="validation-message" id="emailValidation"></div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group half">
                            <label for="password">Password</label>
                            <div class="input-wrapper">
                                <i class="fas fa-lock input-icon"></i>
                                <input type="password" id="password" name="password" required autocomplete="new-password">
                                <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <div class="input-focus-border"></div>
                            </div>
                        </div>
                        <div class="form-group half">
                            <label for="confirmPassword">Confirm Password</label>
                            <div class="input-wrapper">
                                <i class="fas fa-lock input-icon"></i>
                                <input type="password" id="confirmPassword" name="confirmPassword" required autocomplete="new-password">
                                <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <div class="input-focus-border"></div>
                                <div class="validation-icon" id="confirmPasswordIcon"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Compact Password Strength Indicator -->
                    <div class="password-strength compact" id="passwordStrength">
                        <div class="strength-bar">
                            <div class="strength-fill"></div>
                        </div>
                        <div class="strength-text">Password strength</div>
                    </div>

                    <div class="form-row">
                        <div class="form-group half">
                            <label for="pin">Security PIN (5 digits)</label>
                            <div class="input-wrapper">
                                <i class="fas fa-shield-alt input-icon"></i>
                                <input type="text" id="pin" name="pin" required maxlength="5" pattern="[0-9]{5}"
                                       placeholder="12345" autocomplete="off">
                                <div class="input-focus-border"></div>
                                <div class="validation-icon" id="pinIcon"></div>
                            </div>
                            <div class="validation-message" id="pinValidation"></div>
                        </div>
                        <div class="form-group half">
                            <label for="referredBy">Referred By (Optional)</label>
                            <div class="input-wrapper">
                                <i class="fas fa-user-friends input-icon"></i>
                                <input type="text" id="referredBy" name="referredBy" placeholder="Referral code" autocomplete="off">
                                <div class="input-focus-border"></div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-container">
                            <input type="checkbox" id="terms" name="terms" required>
                            <span class="checkmark"></span>
                            I agree to the <a href="#" class="terms-link">Terms of Service</a> and <a href="#" class="privacy-link">Privacy Policy</a>
                        </label>
                    </div>

                    <button type="submit" class="btn btn-primary" id="registerBtn" disabled>
                        <span class="btn-text">Create Account</span>
                        <div class="btn-loader">
                            <div class="spinner"></div>
                        </div>
                    </button>
                </form>

                <div class="auth-footer">
                    <p>Already have an account? <a href="login.php" class="login-link">Sign In</a></p>
                </div>
            </div>
        </div>

        <!-- Message Container -->
        <div id="message" class="message-container"></div>
    </div>

    <script src="user/js/csrf-manager.js"></script>
    <script src="user/js/modern-auth.js"></script>
    <script>
        // Handle referral query parameter
        document.addEventListener('DOMContentLoaded', function() {
            const urlParams = new URLSearchParams(window.location.search);
            const referralCode = urlParams.get('by');

            if (referralCode) {
                const referredByInput = document.getElementById('referredBy');
                if (referredByInput) {
                    referredByInput.value = referralCode;
                    // Add a subtle highlight to show it was prefilled
                    referredByInput.style.background = 'rgba(99, 102, 241, 0.05)';
                    referredByInput.style.borderColor = 'rgba(99, 102, 241, 0.3)';
                }
            }
        });
    </script>
</body>
</html>
