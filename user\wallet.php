<?php
// Include PSR-4 autoloader
require_once __DIR__ . '/../autoload.php';

use Frontend\Config\FrontendConfig;
use Frontend\Services\SessionService;

// Initialize PSR-4 configuration and session
FrontendConfig::init();
SessionService::init();

// Check if user is logged in
if (!SessionService::isAuthenticated()) {
    header('Location: ../index.php');
    exit;
}

$user = SessionService::getCurrentUser();

// Set variables for header
$pageTitle = 'My Wallet - TLS Wallet';
$currentPage = 'wallet';
$basePath = '.';
$cssPath = 'css';

// Include header
include '../includes/header.php';
?>            <!-- Message Display Area -->
            <div id="message" class="message" style="display: none;"></div>
            
            <!-- Wallet Content -->
            <div class="wallet-page">
                <div class="page-header">
                    <h2>My Wallet & Withdrawals</h2>
                    <p>View your investment earnings and withdraw funds</p>
                </div>                <!-- Combined Wallet & Withdrawal Card -->
                <div class="card">
                    <h3>Investment Earnings</h3>
                      <!-- Investment Earnings Section -->
                    <div class="wallet-info">                        
                        <div class="wallet-field">
                            <label for="walletBalance">Total Earnings from Investments</label>
                            <div class="balance-display">
                                <input type="text" id="accountBalance" readonly value="Loading... USDT" class="wallet-balance-input">
                            </div>
                            <div class="balance-display">
                                <input type="text" id="referalBonus" readonly value="Loading... USDT" class="wallet-balance-input">
                            </div>
                            <div class="balance-display">
                                <input type="text" id="walletBalance" readonly value="Loading... USDT" class="wallet-balance-input">
                            </div>
                        </div>
                        
                        <!-- Info about unique deposit addresses -->
                        <div class="wallet-info-note">
                            <p><strong>Note:</strong> This shows your total earnings from investments. You can withdraw these earnings using the form below.</p>
                        </div>
                    </div><!-- Withdrawal Section -->
                    <div class="withdrawal-section">
                        <h4>Withdraw Funds</h4>
                        <form id="withdrawForm" class="withdraw-form">
                            <div class="form-group">
                                <label for="withdrawAddress">Recipient Address</label>
                                <input type="text" id="withdrawAddress" name="address" required 
                                       placeholder="Enter TRON address (T...)">
                            </div>
                            
                            <div class="form-group">
                                <label for="withdrawAmount">Withdrawal Amount (USDT)</label>
                                <input type="number" id="withdrawAmount" name="amount" step="0.01" min="1" required 
                                       placeholder="Enter amount to withdraw">
                            </div>
                            
                            <div class="form-group">
                                <label for="withdrawPin">Withdrawal PIN</label>
                                <input type="password" id="withdrawPin" name="pin" pattern="[0-9]{5}" maxlength="5" minlength="5" required placeholder="Enter 5-digit PIN">
                            </div>
                            
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">Withdraw</button>
                                <button type="reset" class="btn btn-outline">Reset</button>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="card">                        <!-- Withdrawal Instructions -->
                        <div class="withdrawal-instructions">
                            <div class="instructions-header">
                                <h5><i class="info-icon">ℹ️</i> Withdrawal Instructions</h5>
                            </div>
                            <div class="instructions-content">                                <ul class="instructions-list">
                                    <li><strong>USDT TRON Address:</strong> Enter a valid TRON wallet address starting with "T" (34 characters)</li>
                                    <li><strong>Minimum Amount:</strong> <span id="minAmountText">The minimum withdrawal amount is 1.00 USDT</span></li>
                                    <li><strong>Processing Fee:</strong> 10% will be deducted from withdrawal amount to cover processing & network fee</li>
                                    <li><strong>Processing Time:</strong> Withdrawals are typically processed within 5-10 minutes</li>
                                    <li><strong>Verification:</strong> Double-check the recipient address before confirming</li>
                                    <li><strong>Transaction ID:</strong> You'll receive a transaction ID once the withdrawal is processed</li>
                                </ul>
                                <div class="warning-note">
                                    <span class="warning-icon">⚠️</span>
                                    <strong>Important:</strong> Ensure the recipient address is correct. Transactions on the TRON network are irreversible.
                                </div>
                            </div>
                        </div>
                </div>            </div><!-- Enhanced Withdrawal Response Modal -->
            <div id="withdrawalModal" class="modal-overlay" style="display: none;">
                <div class="modal-container">
                    <div class="modal-content">
                        <!-- Modal Icon and Header -->
                        <div class="modal-icon-section" id="modalIconSection">
                            <div class="modal-icon" id="modalIcon">
                                <i class="icon" id="modalIconSymbol"></i>
                            </div>
                        </div>
                        
                        <!-- Modal Header -->
                        <div class="modal-header" id="modalHeader">
                            <h3 id="modalTitle">Withdrawal Status</h3>
                            <button type="button" class="modal-close" id="closeModal" aria-label="Close modal">
                                <svg width="20" height="20" viewBox="0 0 20 20" fill="none">
                                    <path d="M15 5L5 15M5 5L15 15" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                </svg>
                            </button>
                        </div>
                        
                        <!-- Modal Body -->
                        <div class="modal-body">
                            <div id="modalMessage" class="modal-message"></div>
                              <!-- Success Details Section -->
                            <div id="successDetails" class="success-details hidden">
                                <div class="detail-row">
                                    <span class="detail-label">Amount Withdrawn:</span>
                                    <span class="detail-value" id="withdrawnAmount">-</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">Network Fee (10%):</span>
                                    <span class="detail-value" id="networkFee">-</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">Amount Received:</span>
                                    <span class="detail-value highlight" id="receivedAmount">-</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">Recipient Address:</span>
                                    <span class="detail-value address" id="recipientAddress">-</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">Transaction ID:</span>
                                    <span class="detail-value txid" id="transactionId">Processing...</span>
                                </div>
                            </div>
                            
                            <!-- Error Details Section -->
                            <div id="errorDetails" class="error-details" style="display: none;">
                                <div class="error-explanation" id="errorExplanation"></div>
                                <div class="error-suggestions" id="errorSuggestions"></div>
                            </div>
                            
                            <!-- Action Buttons for Success -->
                            <div id="successActions" class="modal-actions" style="display: none;">
                                <button type="button" class="btn btn-outline" id="copyDetailsBtn">
                                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                        <path d="M5.5 2.5h-2a1 1 0 0 0-1 1v9a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1v-2" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                                        <rect x="6.5" y="1.5" width="7" height="7" rx="1" stroke="currentColor" stroke-width="1.5"/>
                                    </svg>
                                    Copy Details
                                </button>
                                <button type="button" class="btn btn-primary" id="continueBtn">
                                    Continue
                                </button>
                            </div>
                        </div>
                        
                        <!-- Modal Footer -->
                        <div class="modal-footer">
                            <button type="button" class="btn btn-primary" id="okModalBtn">
                                OK
                            </button>
                            <button type="button" class="btn btn-secondary" id="tryAgainBtn" style="display: none;">
                                Try Again
                            </button>
                        </div>
                    </div>
                </div>
            </div>

<?php 
// Include footer
include '../includes/footer.php';
?>
    <style>
        /* Withdrawal Section Styling */
        .withdrawal-section {
            margin-top: 30px;
            padding-top: 30px;
            border-top: 2px solid #e9ecef;
        }

        .withdrawal-section h4 {
            margin: 0 0 20px 0;
            color: #2c3e50;
            font-size: 1.2rem;
            font-weight: 600;
        }        .withdraw-form {
            margin: 0;
        }

        .wallet-info {
            margin-bottom: 0;
        }

        /* Withdrawal Instructions Styling */
        .withdrawal-instructions {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            margin-bottom: 25px;
            overflow: hidden;
        }

        .instructions-header {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            padding: 12px 20px;
            margin: 0;
        }

        .instructions-header h5 {
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .info-icon {
            font-size: 1.1rem;
        }

        .instructions-content {
            padding: 20px;
        }

        .instructions-list {
            margin: 0 0 15px 0;
            padding-left: 20px;
            list-style: none;
        }

        .instructions-list li {
            margin-bottom: 12px;
            padding-left: 20px;
            position: relative;
            color: #495057;
            line-height: 1.5;
        }

        .instructions-list li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
            font-size: 1.1rem;
        }

        .instructions-list li strong {
            color: #2c3e50;
        }

        .warning-note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 12px 15px;
            margin-top: 15px;
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }

        .warning-icon {
            font-size: 1.1rem;
            margin-top: 2px;
        }

        .warning-note strong {
            color: #856404;
        }

        /* Enhanced card styling for combined layout */
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 30px;
            margin-bottom: 24px;
            border: 1px solid #e9ecef;
        }

        .card h3 {
            margin: 0 0 25px 0;
            color: #2c3e50;
            font-size: 1.4rem;
            font-weight: 600;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }

        /* Form styling improvements */
        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .form-actions {
            display: flex;
            gap: 12px;
            margin-top: 25px;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            text-decoration: none;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2980b9 0%, #1c5985 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(52, 152, 219, 0.3);
        }

        .btn-outline {
            background: transparent;
            color: #6c757d;
            border: 2px solid #e9ecef;
        }

        .btn-outline:hover {
            background: #f8f9fa;
            border-color: #6c757d;
        }

        /* Wallet info styling */
        .wallet-field {
            margin-bottom: 20px;
        }

        .wallet-field label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }

        .address-display, .balance-display {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .wallet-address-input, .wallet-balance-input {
            flex: 1;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
            font-family: 'Courier New', monospace;
        }

        .btn-sm {
            padding: 8px 16px;
            font-size: 0.9rem;
        }

        /* Page header styling */
        .page-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .page-header h2 {
            margin: 0 0 8px 0;
            color: #2c3e50;
            font-size: 2rem;
            font-weight: 700;
        }

        .page-header p {
            margin: 0;
            color: #6c757d;
            font-size: 1.1rem;
        }        /* Responsive design */
        @media (max-width: 768px) {
            .card {
                padding: 20px;
            }
            
            .form-actions {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
                text-align: center;
            }
            
            .address-display, .balance-display {
                flex-direction: column;
                align-items: stretch;
            }

            .instructions-content {
                padding: 15px;
            }

            .instructions-list {
                padding-left: 15px;
            }

            .instructions-list li {
                padding-left: 15px;
                font-size: 0.95rem;
            }

            .warning-note {
                padding: 10px 12px;
                font-size: 0.95rem;
            }
        }        /* Enhanced Withdrawal Modal Styles */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            backdrop-filter: blur(6px);
            animation: fadeIn 0.3s ease-out;
        }

        .modal-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 80px rgba(0, 0, 0, 0.25);
            max-width: 520px;
            width: 90%;
            max-height: 90vh;
            overflow: hidden;
            animation: slideInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
            position: relative;
        }

        .modal-content {
            display: flex;
            flex-direction: column;
        }

        /* Modal Icon Section */
        .modal-icon-section {
            padding: 30px 30px 20px;
            text-align: center;
            position: relative;
        }

        .modal-icon {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            margin-bottom: 15px;
            position: relative;
            animation: iconBounce 0.6s ease-out 0.2s both;
        }

        .modal-icon.success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            box-shadow: 0 8px 30px rgba(40, 167, 69, 0.3);
        }

        .modal-icon.error {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            box-shadow: 0 8px 30px rgba(220, 53, 69, 0.3);
        }

        .modal-icon.warning {
            background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            color: white;
            box-shadow: 0 8px 30px rgba(255, 193, 7, 0.3);
        }

        .modal-icon.info {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            box-shadow: 0 8px 30px rgba(23, 162, 184, 0.3);
        }

        /* Success animation ring */
        .modal-icon.success::after {
            content: '';
            position: absolute;
            top: -10px;
            left: -10px;
            right: -10px;
            bottom: -10px;
            border: 3px solid #28a745;
            border-radius: 50%;
            opacity: 0;
            animation: successRing 0.8s ease-out 0.5s both;
        }

        .modal-header {
            padding: 0 30px 20px;
            text-align: center;
            position: relative;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 1.5rem;
            font-weight: 700;
            color: #2c3e50;
            animation: titleSlide 0.5s ease-out 0.3s both;
        }

        .modal-close {
            position: absolute;
            top: -10px;
            right: 10px;
            background: none;
            border: none;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 8px;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
            color: #6c757d;
        }

        .modal-close:hover {
            background: rgba(0, 0, 0, 0.05);
            color: #495057;
            transform: scale(1.1);
        }

        .modal-body {
            padding: 0 30px 25px;
            text-align: center;
        }

        .modal-message {
            font-size: 1.1rem;
            padding: 0;
            margin-bottom: 25px;
            line-height: 1.6;
            color: #495057;
            animation: messageSlide 0.5s ease-out 0.4s both;
        }        /* Success Details */
        .success-details {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border: 1px solid #e9ecef;
            border-radius: 16px;
            padding: 24px;
            margin: 24px 0;
            text-align: left;
            animation: detailsSlide 0.5s ease-out 0.5s both;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        }

        .success-details.hidden {
            display: none !important;
        }

        .success-details.visible {
            display: block !important;
        }

        .detail-row {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            padding: 12px 0;
            border-bottom: 1px solid #e9ecef;
            min-height: 48px;
        }

        .detail-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }

        .detail-row:first-child {
            padding-top: 0;
        }

        .detail-label {
            font-weight: 600;
            color: #495057;
            font-size: 0.95rem;
            line-height: 1.4;
            flex-shrink: 0;
            min-width: 140px;
            padding-right: 16px;
        }

        .detail-value {
            font-weight: 600;
            color: #2c3e50;
            font-size: 0.95rem;
            text-align: right;
            flex: 1;
            line-height: 1.4;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .detail-value.highlight {
            color: #28a745;
            font-size: 1.1rem;
            font-weight: 700;
            background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
            padding: 8px 12px;
            border-radius: 8px;
            border: 1px solid #b4d7c1;
        }

        .detail-value.address {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.8rem;
            background: #f8f9fa;
            padding: 8px 10px;
            border-radius: 6px;
            border: 1px solid #e9ecef;
            color: #495057;
            line-height: 1.2;
            word-break: break-all;
            max-width: 200px;
        }

        .detail-value.txid {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.8rem;
            color: #17a2b8;
            background: #e7f3ff;
            padding: 8px 10px;
            border-radius: 6px;
            border: 1px solid #bee5eb;
            cursor: pointer;
            transition: all 0.2s ease;
            line-height: 1.2;
        }

        .detail-value.txid:hover {
            background: #d1ecf1;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(23, 162, 184, 0.2);
        }

        /* Special styling for amount rows */
        .detail-row.amount-row {
            background: #fff;
            margin: 0 -8px;
            padding: 12px 8px;
            border-radius: 8px;
            border: 1px solid #f1f3f4;
        }

        .detail-row.fee-row {
            background: #fef9e7;
            margin: 0 -8px;
            padding: 12px 8px;
            border-radius: 8px;
            border: 1px solid #ffeaa7;
        }

        .detail-row.received-row {
            background: #f0f9f0;
            margin: 0 -8px;
            padding: 12px 8px;
            border-radius: 8px;
            border: 1px solid #d4edda;
        }

        /* Error Details */
        .error-details {
            text-align: left;
            margin: 20px 0;
            animation: detailsSlide 0.5s ease-out 0.5s both;
        }

        .error-explanation {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            font-size: 0.95rem;
            line-height: 1.5;
            color: #856404;
        }

        .error-suggestions {
            background: #e2e3e5;
            border-radius: 8px;
            padding: 15px;
            font-size: 0.9rem;
            line-height: 1.5;
            color: #383d41;
        }

        .error-suggestions ul {
            margin: 10px 0 0 0;
            padding-left: 20px;
        }

        .error-suggestions li {
            margin-bottom: 5px;
        }        /* Modal Actions */
        .modal-actions {
            display: flex;
            gap: 16px;
            justify-content: center;
            margin: 24px 0 16px;
            animation: actionsSlide 0.5s ease-out 0.6s both;
        }

        .modal-actions .btn {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 24px;
            font-size: 0.95rem;
            font-weight: 600;
            border-radius: 10px;
            transition: all 0.3s ease;
            min-width: 140px;
            justify-content: center;
        }

        .modal-actions .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .modal-actions .btn svg {
            flex-shrink: 0;
        }        .modal-footer {
            padding: 24px 30px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-top: 1px solid #e9ecef;
            display: flex;
            gap: 16px;
            justify-content: center;
        }

        .modal-footer .btn {
            min-width: 120px;
            padding: 14px 28px;
            font-weight: 600;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .modal-footer .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            border: none;
        }

        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-1px);
        }

        /* Enhanced Animations */
        @keyframes fadeIn {
            from { 
                opacity: 0; 
            }
            to { 
                opacity: 1; 
            }
        }

        @keyframes slideInScale {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        @keyframes iconBounce {
            from {
                opacity: 0;
                transform: scale(0.3) rotate(-180deg);
            }
            60% {
                transform: scale(1.1) rotate(0deg);
            }
            to {
                opacity: 1;
                transform: scale(1) rotate(0deg);
            }
        }

        @keyframes successRing {
            from {
                opacity: 0;
                transform: scale(0.8);
            }
            50% {
                opacity: 0.8;
                transform: scale(1.1);
            }
            to {
                opacity: 0;
                transform: scale(1.3);
            }
        }

        @keyframes titleSlide {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes messageSlide {
            from {
                opacity: 0;
                transform: translateY(15px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes detailsSlide {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes actionsSlide {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
            gap: 12px;
            justify-content: flex-end;
        }

        .modal-footer .btn {
            min-width: 100px;
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-30px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }        /* Responsive Design for Enhanced Modal */
        @media (max-width: 576px) {
            .modal-container {
                width: 95%;
                margin: 20px;
                max-width: none;
            }

            .modal-icon-section {
                padding: 25px 20px 15px;
            }

            .modal-icon {
                width: 70px;
                height: 70px;
                font-size: 2rem;
            }

            .modal-header, .modal-body, .modal-footer {
                padding: 15px 20px;
            }

            .modal-header h3 {
                font-size: 1.3rem;
            }

            .modal-message {
                font-size: 1rem;
            }

            .success-details {
                padding: 16px;
                margin: 16px 0;
            }

            .detail-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 8px;
                padding: 12px 0;
                min-height: auto;
            }

            .detail-label {
                min-width: auto;
                padding-right: 0;
                margin-bottom: 4px;
            }

            .detail-value {
                text-align: left;
                width: 100%;
                margin-left: 0;
            }

            .detail-value.address {
                max-width: 100%;
                font-size: 0.75rem;
            }

            .detail-value.highlight {
                font-size: 1rem;
                padding: 6px 10px;
            }

            .modal-actions {
                flex-direction: column;
                gap: 12px;
            }

            .modal-actions .btn {
                width: 100%;
                min-width: auto;
                justify-content: center;
            }

            .modal-footer {
                flex-direction: column;
                padding: 16px 20px;
            }

            .modal-footer .btn {
                width: 100%;
                min-width: auto;
            }

            /* Special mobile styling for detail rows */
            .detail-row.amount-row,
            .detail-row.fee-row,
            .detail-row.received-row {
                margin: 8px -8px;
                padding: 12px 8px;
            }
        }

        /* Wallet Info Note Styling */
        .wallet-info-note {
            margin-top: 15px;
            padding: 12px 16px;
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 6px;
            color: #1565c0;
        }

        .wallet-info-note p {
            margin: 0;
            font-size: 0.95rem;
            line-height: 1.4;
        }        .wallet-info-note strong {
            color: #0d47a1;
        }

        /* Message Display Styles */
        .message {
            margin: 10px 0;
            padding: 12px 16px;
            border-radius: 6px;
            font-size: 0.95rem;
            line-height: 1.4;
        }

        .message.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .message.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }        .message.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        /* Withdrawal Form Validation Styles */
        .form-group input.invalid {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }

        .form-group input.valid {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }

        .form-group input.invalid:focus {
            border-color: #dc3545;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }

        .form-group input.valid:focus {
            border-color: #28a745;
            box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
        }
    </style>    <script src="js/qr-generator.js"></script>
    <script src="js/wallet.js"></script>
</body>
</html>
