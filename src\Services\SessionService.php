<?php

declare(strict_types=1);

namespace Frontend\Services;

use Frontend\Config\FrontendConfig;

/**
 * PSR-4 compliant session management service
 * Handles all session-related operations with security features
 */
class SessionService
{
    private static bool $initialized = false;

    /**
     * Initialize session with security settings
     */    public static function init(): void
    {
        if (self::$initialized || session_status() !== PHP_SESSION_NONE) {
            return;
        }

        // Skip session initialization in CLI context
        if (php_sapi_name() === 'cli') {
            self::$initialized = true;
            return;
        }

        // Configure session settings
        ini_set('session.cookie_lifetime', (string)FrontendConfig::get('SESSION_LIFETIME', 3600));
        ini_set('session.cookie_secure', FrontendConfig::get('SECURE_COOKIES', false) ? '1' : '0');
        ini_set('session.cookie_httponly', '1');
        ini_set('session.cookie_samesite', 'Lax');
        ini_set('session.use_strict_mode', '1');
        ini_set('session.use_only_cookies', '1');

        session_name(FrontendConfig::get('SESSION_NAME', 'tls_session'));
        session_start();

        // Regenerate session ID for security
        if (!isset($_SESSION['regenerated'])) {
            session_regenerate_id(true);
            $_SESSION['regenerated'] = true;
            $_SESSION['created_at'] = time();
        }

        // Check session timeout
        self::checkTimeout();

        self::$initialized = true;
    }

    /**
     * Check if session has timed out
     */
    private static function checkTimeout(): void
    {
        $maxLifetime = FrontendConfig::get('SESSION_LIFETIME', 3600);
        $createdAt = $_SESSION['created_at'] ?? time();

        if ((time() - $createdAt) > $maxLifetime) {
            self::destroy();
        }
    }

    /**
     * Set user session data after authentication
     */
    public static function setUser(array $userData): void
    {
        self::init();
        
        $_SESSION['user_id'] = $userData['id'];
        $_SESSION['email'] = $userData['email'] ?? '';
        $_SESSION['is_admin'] = $userData['is_admin'] ?? false;
        $_SESSION['token'] = $userData['token'] ?? '';
        $_SESSION['referral_code'] = $userData['referral_code'] ?? '';
        $_SESSION['login_time'] = time();
        $_SESSION['created_at'] = $userData['created_at'];
        
        // Regenerate session ID after login
        session_regenerate_id(true);
        $_SESSION['regenerated'] = true;
    }

    /**
     * Check if user is authenticated
     */
    public static function isAuthenticated(): bool
    {
        self::init();
        return isset($_SESSION['user_id']) && isset($_SESSION['token']) && !empty($_SESSION['token']);
    }

    /**
     * Check if user is admin
     */
    public static function isAdmin(): bool
    {
        return self::isAuthenticated() && ($_SESSION['is_admin'] ?? false);
    }

    /**
     * Get current user data
     */
    public static function getCurrentUser(): ?array
    {
        if (!self::isAuthenticated()) {
            return null;
        }

        return [
            'id' => $_SESSION['user_id'],
            'email' => $_SESSION['email'] ?? '',
            'is_admin' => $_SESSION['is_admin'] ?? false,
            'token' => $_SESSION['token'] ?? '',
            'login_time' => $_SESSION['login_time'] ?? null,
            'referral_code' => $_SESSION['referral_code'] ?? '', // Add referral code
            'created_at' => $_SESSION['created_at'] ?? '',
        ];
    }

    /**
     * Get user ID
     */
    public static function getUserId(): ?int
    {
        if (!self::isAuthenticated()) {
            return null;
        }

        return (int)$_SESSION['user_id'];
    }

    /**
     * Get authentication token
     */
    public static function getToken(): ?string
    {
        if (!self::isAuthenticated()) {
            return null;
        }

        return $_SESSION['token'];
    }

    /**
     * Update session token
     */
    public static function updateToken(string $token): void
    {
        self::init();
        $_SESSION['token'] = $token;
    }

    /**
     * Clear user session data (logout)
     */
    public static function clearUser(): void
    {
        self::init();
        
        unset($_SESSION['user_id']);
        unset($_SESSION['email']);
        unset($_SESSION['is_admin']);
        unset($_SESSION['token']);
        unset($_SESSION['login_time']);
        
        // Regenerate session ID after logout
        session_regenerate_id(true);
        $_SESSION['regenerated'] = true;
    }

    /**
     * Destroy session completely
     */
    public static function destroy(): void
    {
        if (session_status() === PHP_SESSION_ACTIVE) {
            session_destroy();
        }
        
        // Clear session cookie
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(
                session_name(),
                '',
                time() - 42000,
                $params["path"],
                $params["domain"],
                $params["secure"],
                $params["httponly"]
            );
        }
        
        self::$initialized = false;
    }

    /**
     * Generate CSRF token
     */
    public static function generateCsrfToken(): string
    {
        self::init();
        
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        
        return $_SESSION['csrf_token'];
    }

    /**
     * Verify CSRF token
     */
    public static function verifyCsrfToken(string $token): bool
    {
        self::init();
        
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }

    /**
     * Set flash message
     */
    public static function setFlash(string $type, string $message): void
    {
        self::init();
        
        if (!isset($_SESSION['flash'])) {
            $_SESSION['flash'] = [];
        }
        
        $_SESSION['flash'][] = [
            'type' => $type,
            'message' => $message,
            'timestamp' => time()
        ];
    }

    /**
     * Get flash messages and clear them
     */
    public static function getFlashMessages(): array
    {
        self::init();
        
        $messages = $_SESSION['flash'] ?? [];
        unset($_SESSION['flash']);
        
        return $messages;
    }

    /**
     * Set session data
     */
    public static function set(string $key, mixed $value): void
    {
        self::init();
        $_SESSION[$key] = $value;
    }

    /**
     * Get session data
     */
    public static function get(string $key, mixed $default = null): mixed
    {
        self::init();
        return $_SESSION[$key] ?? $default;
    }

    /**
     * Check if session key exists
     */
    public static function has(string $key): bool
    {
        self::init();
        return isset($_SESSION[$key]);
    }

    /**
     * Remove session key
     */
    public static function remove(string $key): void
    {
        self::init();
        unset($_SESSION[$key]);
    }

    /**
     * Get session info for debugging
     */
    public static function getSessionInfo(): array
    {
        self::init();
        
        return [
            'session_id' => session_id(),
            'session_name' => session_name(),
            'is_authenticated' => self::isAuthenticated(),
            'is_admin' => self::isAdmin(),
            'created_at' => $_SESSION['created_at'] ?? null,
            'login_time' => $_SESSION['login_time'] ?? null,
            'regenerated' => $_SESSION['regenerated'] ?? false
        ];
    }
}
