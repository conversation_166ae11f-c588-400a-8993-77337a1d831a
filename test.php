<?php
/**
 * TLS Frontend Test Script
 * 
 * Simple test to verify that the frontend is properly configured
 * and all dependencies are working correctly.
 */

echo "TLS Crypto Wallet Frontend Test\n";
echo "===============================\n\n";

// Test 1: PHP Version
$phpVersion = phpversion();
echo "1. PHP Version: $phpVersion ";
if (version_compare($phpVersion, '7.4.0', '>=')) {
    echo "✓ OK\n";
} else {
    echo "✗ PHP 7.4+ required\n";
    exit(1);
}

// Test 2: Required Extensions
$requiredExtensions = ['curl', 'json', 'session'];
echo "2. Required Extensions:\n";
foreach ($requiredExtensions as $ext) {
    echo "   - $ext: ";
    if (extension_loaded($ext)) {
        echo "✓ Loaded\n";
    } else {
        echo "✗ Missing\n";
        exit(1);
    }
}

// Test 3: Autoloader
echo "3. Autoloader: ";
if (file_exists(__DIR__ . '/autoload.php')) {
    require_once __DIR__ . '/autoload.php';
    echo "✓ OK\n";
} else {
    echo "✗ autoload.php not found\n";
    exit(1);
}

// Test 4: Configuration
echo "4. Configuration: ";
if (file_exists(__DIR__ . '/config.php')) {
    require_once __DIR__ . '/config.php';
    echo "✓ OK\n";
    
    // Check if constants are defined
    $requiredConstants = ['APP_NAME', 'API_BASE_URL', 'SESSION_NAME'];
    foreach ($requiredConstants as $const) {
        echo "   - $const: ";
        if (defined($const)) {
            echo "✓ " . constant($const) . "\n";
        } else {
            echo "✗ Not defined\n";
        }
    }
} else {
    echo "✗ config.php not found\n";
    exit(1);
}

// Test 5: Core Classes
echo "5. Core Classes:\n";
$coreClasses = [
    'Frontend\Config\FrontendConfig',
    'Frontend\Services\SessionService',
    'Frontend\Services\ApiService'
];

foreach ($coreClasses as $class) {
    echo "   - $class: ";
    if (class_exists($class)) {
        echo "✓ Found\n";
    } else {
        echo "✗ Not found\n";
        exit(1);
    }
}

// Test 6: Directory Permissions
echo "6. Directory Permissions:\n";
$directories = [
    'logs' => ['read' => true, 'write' => true],
    'src' => ['read' => true, 'write' => false],
    'css' => ['read' => true, 'write' => false]
];

foreach ($directories as $dir => $permissions) {
    if (is_dir($dir)) {
        echo "   - $dir: ";
        $readable = is_readable($dir);
        $writable = is_writable($dir);
        
        if ($permissions['read'] && !$readable) {
            echo "✗ Not readable\n";
        } elseif ($permissions['write'] && !$writable) {
            echo "✗ Not writable\n";
        } else {
            echo "✓ OK\n";
        }
    } else {
        echo "   - $dir: ⚠ Directory not found\n";
    }
}

// Test 7: Session Functionality
echo "7. Session Test: ";
try {
    // Test session functionality
    FrontendConfig::init();
    SessionService::init();
    echo "✓ Sessions working\n";
} catch (Exception $e) {
    echo "✗ Session error: " . $e->getMessage() . "\n";
}

echo "\n";
echo "Test Summary:\n";
echo "=============\n";
echo "✓ All tests passed - Frontend is ready!\n";
echo "\n";
echo "You can now:\n";
echo "1. Access index.php in your web browser\n";
echo "2. Test the login/registration functionality\n";
echo "3. Configure your API endpoints in config.php\n";
echo "\n";
echo "For production deployment:\n";
echo "- Set SHOW_ERRORS to false in config.php\n";
echo "- Enable SECURE_COOKIES if using HTTPS\n";
echo "- Configure proper error logging\n";
