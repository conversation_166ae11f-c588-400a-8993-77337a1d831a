<?php
/**
 * API Wrapper - Frontend Compatible Entry Point
 * 
 * This file provides backward compatibility for the API service
 */

// Include frontend autoloader
require_once __DIR__ . '/autoload.php';

use Frontend\Services\ApiService;
use Frontend\Config\FrontendConfig;
use Frontend\Services\SessionService;

// Initialize configuration
FrontendConfig::init();

/**
 * Backward compatible APIWrapper class
 * Delegates to PSR-4 ApiService for actual functionality
 */
class APIWrapper {
    private ApiService $apiService;

    public function __construct($baseUrl = null) {
        $this->apiService = new ApiService($baseUrl);
    }

    public function setToken($token) {
        $this->apiService->setToken($token);
    }

    // Delegate all method calls to the PSR-4 ApiService
    public function __call($method, $arguments) {
        return $this->apiService->$method(...$arguments);
    }
}

// Handle direct API calls via query string (for frontend JavaScript)
if (isset($_GET['action'])) {
    SessionService::init();
    header('Content-Type: application/json');
    
    $action = $_GET['action'];
    $api = new APIWrapper();
    
    // Get request data for POST requests
    $data = null;
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = file_get_contents('php://input');
        $data = json_decode($input, true);
    }
    
    try {
        switch ($action) {
            case 'get_balance':
                $result = $api->getBalance();
                break;
                
            case 'get_transaction_statistics':
                $result = $api->getTransactionStatistics();
                break;
                
            case 'get_transactions':
                $limit = $_GET['limit'] ?? $data['limit'] ?? 10;
                $page = $_GET['page'] ?? $data['page'] ?? 0;
                $type = $_GET['type'] ?? $data['type'] ?? null;
                
                $result = $api->getTransactions($type, $limit, $page);
                break;
                
            default:
                $result = ['error' => 'Unknown action: ' . $action];
                break;
        }
        
        echo json_encode($result);
        
    } catch (Exception $e) {
        echo json_encode(['error' => 'API Error: ' . $e->getMessage()]);
    }
}
