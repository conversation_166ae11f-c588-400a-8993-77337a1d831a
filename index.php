<?php
// Include frontend autoloader
require_once __DIR__ . '/autoload.php';

use Frontend\Config\FrontendConfig;
use Frontend\Services\SessionService;

// Initialize configuration and session
FrontendConfig::init();
SessionService::init();

// Check if user is logged in
$isLoggedIn = SessionService::isAuthenticated();
$isAdmin = SessionService::isAdmin();

// Redirect to admin if admin user
if ($isLoggedIn && $isAdmin && !isset($_GET['user_view'])) {
    header('Location: admin.php');
    exit;
}

// Redirect to dashboard if logged in
if ($isLoggedIn && !isset($_GET['logout'])) {
    header('Location: user/dashboard.php');
    exit;
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: index.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - TLS Wallet</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/auth.css">
</head>
<body>
    <div class="auth-container">        <div class="auth-header">
            <h1>TLS Wallet</h1>
            <p>TRON · USDT · Secure cryptocurrency wallet management</p>
        </div>

        <div class="auth-tabs">
            <button class="tab-btn active" onclick="showLogin()">Login</button>
            <button class="tab-btn" onclick="showRegister()">Register</button>
        </div>

        <!-- Login Form -->
        <form id="loginForm" class="auth-form active">
            <div class="form-group">
                <label for="loginEmail">Email</label>
                <input type="email" id="loginEmail" name="email" required>
            </div>
            <div class="form-group">
                <label for="loginPassword">Password</label>
                <input type="password" id="loginPassword" name="password" required>
            </div>
            <button type="submit" class="btn btn-primary">Login</button>
            <div class="auth-links">
                <a href="#" onclick="showForgotPassword()">Forgot Password?</a>
            </div>
        </form>

        <!-- Register Form -->
        <form id="registerForm" class="auth-form">
            <div class="form-group">
                <label for="registerEmail">Email</label>
                <input type="email" id="registerEmail" name="email" required>
            </div>
            <div class="form-group">
                <label for="registerPassword">Password</label>
                <input type="password" id="registerPassword" name="password" required minlength="6" pattern="[A-Za-z0-9]+" title="Password must be 6+ characters, letters and numbers only">
                <div class="field-help">Minimum 6 characters, letters and numbers only</div>
            </div>
            <div class="form-group">
                <label for="confirmPassword">Confirm Password</label>
                <input type="password" id="confirmPassword" name="confirmPassword" required>
            </div>
            <div class="form-group">
                <label for="registerPin">PIN (5 digits)</label>
                <input type="password" id="registerPin" name="pin" pattern="[0-9]{5}" maxlength="5" minlength="5" required autocomplete="off" placeholder="Enter 5-digit PIN">
            </div>
            <div class="form-group">
                <label for="registerReferral">Referred By</label>
                <input type="text" id="registerReferral" name="referral" placeholder="Referral code (optional)">
            </div>
            <button type="submit" class="btn btn-primary">Register</button>
        </form>

        <!-- Forgot Password Form -->
        <form id="forgotPasswordForm" class="auth-form">
            <div class="form-group">
                <label for="forgotEmail">Email</label>
                <input type="email" id="forgotEmail" name="email" required>
            </div>
            <button type="submit" class="btn btn-primary">Send Reset Link</button>
            <div class="auth-links">
                <a href="#" onclick="showLogin()">Back to Login</a>
            </div>
        </form>

        <div id="message" class="message"></div>    </div>    <script src="user/js/csrf-manager.js"></script>
    <script src="user/js/auth.js"></script>
</body>
</html>
