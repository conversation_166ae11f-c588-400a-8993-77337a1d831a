<?php
// Simple AJAX handler for TLS Frontend
// No external dependencies - pure PHP implementation

error_reporting(0);
ini_set('display_errors', 0);
ob_start();

// Include frontend autoloader
require_once __DIR__ . '/autoload.php';

use Frontend\Config\FrontendConfig;
use Frontend\Services\SessionService;
use Frontend\Services\ApiService;
use Frontend\Utils\ValidationUtils;

try {
    // Initialize configuration and session
    FrontendConfig::init();
    SessionService::init();
    
    // Generate CSRF token if needed
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    
} catch (Exception $e) {
    log_error("AJAX Initialization Error: " . $e->getMessage(), 'ajax_errors.log');
    echo json_encode([
        'error' => 'System initialization failed',
        'code' => 500,
        'message' => 'Please refresh the page and try again'
    ]);
    exit;
}

// Set JSON content type
header('Content-Type: application/json');

try {
    // Get JSON input
    $input = json_decode(file_get_contents('php://input'), true);
    $input = ValidationUtils::sanitizeInput($input ?? []);
    $action = $input['action'] ?? '';
    
    if (empty($action)) {
        echo json_encode(['error' => 'No action specified']);
        exit;
    }
    
    $api = new ApiService();
    
    // Define state-changing operations that require CSRF protection
    $stateChangingActions = [
        'register', 'login', 'change_password', 'withdraw', 'record_deposit',
        'create_investment', 'admin_promote_user', 'admin_demote_user',
        'admin_activate_user', 'admin_deactivate_user', 'confirm_payment'
    ];
    
    // CSRF validation for state-changing operations
    if (in_array($action, $stateChangingActions)) {
        if (!isset($input['csrf_token']) || !verify_csrf_token($input['csrf_token'])) {
            echo json_encode([
                'error' => 'Invalid or expired CSRF token',
                'code' => 403,
                'action_required' => 'refresh_token'
            ]);
            exit;
        }
    }
    
    // Handle actions
    switch ($action) {
        case 'get_csrf_token':
            $csrf_token = generate_csrf_token();
            echo json_encode([
                'success' => true,
                'csrf_token' => $csrf_token,
                'expires_in' => 3600
            ]);
            break;
        
        case 'register':
            if (!isset($input['email'], $input['password'])) {
                echo json_encode(['error' => 'Missing required fields']);
                break;
            }
            $response = $api->register($input['email'], $input['password'], $input['pin']);
            // Auto-login after successful registration
            if ($response && isset($response['success']) && $response['success'] && isset($response['user'])) {
                $user = $response['user'];
                // Map possible alternative field names
                if (!isset($user['id']) && isset($user['user_id'])) {
                    $user['id'] = $user['user_id'];
                }
                if (!isset($user['email']) && isset($user['username'])) {
                    $user['email'] = $user['username'];
                }
                if (!isset($user['token']) && isset($response['token'])) {
                    $user['token'] = $response['token'];
                }
                if (!isset($user['referral_code']) && isset($response['referral_code'])) {
                    $user['referral_code'] = $response['referral_code'];
                }
                if (!isset($user['is_admin'])) {
                    $user['is_admin'] = false;
                }
                if (isset($user['id'], $user['email'], $user['token'])) {
                    SessionService::setUser($user);
                }
            }
            echo json_encode($response ?: ['error' => 'Registration failed']);
            break;
        
        case 'login':
            if (!isset($input['email'], $input['password'])) {
                echo json_encode(['error' => 'Missing email or password']);
                break;
            }
            $response = $api->login($input['email'], $input['password']);
            // Debug: Log the login API response
            error_log('LOGIN API RESPONSE: ' . var_export($response, true));
            // Attempt to map common backend field names to required frontend fields
            if ($response && isset($response['success']) && $response['success']) {
                if (isset($response['user'])) {
                    $user = $response['user'];
                    // Map possible alternative field names
                    if (!isset($user['id']) && isset($user['user_id'])) {
                        $user['id'] = $user['user_id'];
                    }
                    if (!isset($user['email']) && isset($user['username'])) {
                        $user['email'] = $user['username'];
                    }
                    if (!isset($user['token']) && isset($response['token'])) {
                        $user['token'] = $response['token'];
                    }
                    if (!isset($user['referral_code']) && isset($response['referral_code'])) {
                        $user['referral_code'] = $response['referral_code'];
                    }
                    if (!isset($user['is_admin'])) {
                        $user['is_admin'] = false;
                    }
                    // Ensure required fields are present
                    if (isset($user['id'], $user['email'], $user['token'])) {
                        error_log('LOGIN USER DATA (MAPPED): ' . var_export($user, true));
                        SessionService::setUser($user);
                        error_log('SESSION AFTER LOGIN: ' . var_export($_SESSION, true));
                    } else {
                        error_log('LOGIN: User data missing required fields after mapping: ' . var_export($user, true));
                        echo json_encode(['error' => 'Login failed: Invalid user data']);
                        break;
                    }
                } else {
                    error_log('LOGIN: No user data in response');
                    echo json_encode(['error' => 'Login failed: No user data']);
                    break;
                }
            } else {
                error_log('LOGIN: Failed or no success flag');
            }
            echo json_encode($response ?: ['error' => 'Login failed']);
            break;
        
        case 'logout':
            SessionService::logout();
            echo json_encode(['success' => true]);
            break;
        
        case 'get_balance':
            if (!SessionService::isAuthenticated()) {
                echo json_encode(['error' => 'Not authenticated']);
                break;
            }
            $response = $api->getBalance();
            echo json_encode($response ?: ['error' => 'Failed to get balance']);
            break;
        
        case 'get_transactions':
            if (!SessionService::isAuthenticated()) {
                echo json_encode(['error' => 'Not authenticated']);
                break;
            }
            $limit = $input['limit'] ?? 20;
            $page = $input['page'] ?? 0;
            $type = $input['type'] ?? null;
            $response = $api->getTransactions($type, $limit, $page);
            echo json_encode($response ?: ['error' => 'Failed to get transactions']);
            break;
        
        case 'create_wallet':
            if (!SessionService::isAuthenticated()) {
                echo json_encode(['error' => 'Not authenticated']);
                break;
            }
            $response = $api->createWallet();
            echo json_encode($response ?: ['error' => 'Failed to create wallet']);
            break;
        
        case 'get_withdrawal_config':
            echo json_encode([
                'success' => true,
                'config' => [
                    'min_amount' => (float)FrontendConfig::get('MIN_WITHDRAWAL_AMOUNT', 375.00),
                    'processing_fee_percent' => 10
                ]
            ]);
            break;
        
        case 'withdraw':    
            if (!SessionService::isAuthenticated()) {
                echo json_encode(['error' => 'Not authenticated']);
                break;
            }
            if (!isset($input['amount'], $input['address'], $input['pin'])) {
                echo json_encode(['error' => 'Missing amount, address, or PIN']);
                break;
            }
            // Validate PIN format
            if (!preg_match('/^[0-9]{5}$/', $input['pin'])) {
                echo json_encode(['error' => 'Invalid PIN format. PIN must be 5 digits.']);
                break;
            }
            $response = $api->withdraw($input['address'], (float)$input['amount'], $input['pin']);
            echo json_encode($response ?: ['error' => 'Withdrawal failed']);
            break;
        
        case 'get_investment_plans':
            $response = $api->getInvestmentPlans();
            echo json_encode($response ?: ['error' => 'Failed to get investment plans']);
            break;
        
        case 'create_investment':
            if (!SessionService::isAuthenticated()) {
                echo json_encode(['error' => 'Not authenticated']);
                break;
            }
            if (!isset($input['amount'], $input['plan'])) {
                echo json_encode(['error' => 'Missing amount or plan']);
                break;
            }
            $response = $api->createInvestment((float)$input['amount'], $input['plan']);
            echo json_encode($response ?: ['error' => 'Investment creation failed']);
            break;
        
        case 'get_user_stats':
            if (!SessionService::isAuthenticated()) {
                echo json_encode(['error' => 'Not authenticated']);
                break;
            }
            // This would typically make API calls to get user statistics
            echo json_encode([
                'success' => true,
                'stats' => [
                    'total_balance' => '0.00',
                    'total_invested' => '0.00',
                    'total_withdrawn' => '0.00',
                    'active_investments' => 0
                ]
            ]);
            break;
        
        case 'get_active_investments':
            if (!SessionService::isAuthenticated()) {
                echo json_encode(['error' => 'Not authenticated']);
                break;
            }
            $response = $api->getActiveInvestments();
            echo json_encode($response ?: ['error' => 'Failed to get active investments']);
            break;
        
        // Admin actions
        case 'admin_get_users':
            if (!SessionService::isAuthenticated() || !SessionService::isAdmin()) {
                echo json_encode(['error' => 'Admin access required']);
                break;
            }
            $response = $api->getUserList($input['limit'] ?? 20, $input['offset'] ?? 0);
            echo json_encode($response ?: ['error' => 'Failed to get users']);
            break;
        
        case 'admin_get_stats':
            if (!SessionService::isAuthenticated() || !SessionService::isAdmin()) {
                echo json_encode(['error' => 'Admin access required']);
                break;
            }
            $response = $api->getSystemStatistics();
            echo json_encode($response ?: ['error' => 'Failed to get statistics']);
            break;
        
        case 'get_transaction_statistics':
            if (!SessionService::isAuthenticated()) {
                echo json_encode(['error' => 'Not authenticated']);
                break;
            }
            $response = $api->getTransactionStatistics();
            echo json_encode($response ?: ['error' => 'Failed to get transaction statistics']);
            break;
        
        case 'get_investment_statistics':
            if (!SessionService::isAuthenticated()) {
                echo json_encode(['error' => 'Not authenticated']);
                break;
            }
            if (method_exists($api, 'getInvestmentStatistics')) {
                $response = $api->getInvestmentStatistics();
                echo json_encode($response ?: ['error' => 'Failed to get investment statistics']);
            } else {
                echo json_encode(['error' => 'Investment statistics not implemented']);
            }
            break;
        
        case 'get_wallet_balance':
            if (!SessionService::isAuthenticated()) {
                echo json_encode(['error' => 'Not authenticated']);
                break;
            }
            $response = $api->getWalletBalance();
            echo json_encode($response ?: ['error' => 'Failed to get wallet balance']);
            break;
        
        case 'get_system_configuration':
            // You can add authentication if needed
            $config = [
                'min_withdrawal_amount' => (float)FrontendConfig::get('MIN_WITHDRAWAL_AMOUNT', 375.00),
                'processing_fee_percent' => 10,
                // Add more config values as needed
            ];
            echo json_encode(['success' => true, 'config' => $config]);
            break;
        
        case 'get_investment_history':
            if (!SessionService::isAuthenticated()) {
                echo json_encode(['error' => 'Not authenticated']);
                break;
            }
            if (method_exists($api, 'getInvestmentHistory')) {
                $response = $api->getInvestmentHistory();
                echo json_encode($response ?: ['error' => 'Failed to get investment history']);
            } else {
                echo json_encode(['error' => 'Investment history not implemented']);
            }
            break;
        
        case 'change_password':
            if (!SessionService::isAuthenticated()) {
                echo json_encode(['error' => 'Not authenticated']);
                break;
            }
            if (!isset($input['current_password'], $input['new_password'])) {
                echo json_encode(['error' => 'Missing current or new password']);
                break;
            }
            $response = $api->changePassword($input['current_password'], $input['new_password']);
            echo json_encode($response ?: ['error' => 'Password change failed']);
            break;
        
        case 'create_deposit_wallet':
            if (!SessionService::isAuthenticated()) {
                echo json_encode(['error' => 'Not authenticated']);
                break;
            }
            // Pass all input data to the API method
            $response = $api->createDepositWallet($input);
            echo json_encode($response ?: ['error' => 'Failed to create deposit wallet']);
            break;
        
        case 'get_live_wallet':
            if (!SessionService::isAuthenticated()) {
                echo json_encode(['error' => 'Not authenticated']);
                break;
            }
            if (!isset($input['address'])) {
                echo json_encode(['error' => 'Missing wallet address']);
                break;
            }
            $response = $api->getLiveWalletBalance($input['address']);
            echo json_encode($response ?: ['error' => 'Failed to get live wallet balance']);
            break;
        
        case 'get_wallet':
            if (!SessionService::isAuthenticated()) {
                echo json_encode(['error' => 'Not authenticated']);
                break;
            }
            $response = $api->getWallet();
            echo json_encode($response ?: ['error' => 'Failed to get wallet']);
            break;
        
        case 'get_investment_details':
            if (!SessionService::isAuthenticated()) {
                echo json_encode(['error' => 'Not authenticated']);
                break;
            }
            if (!isset($input['id'])) {
                echo json_encode(['error' => 'Missing investment ID']);
                break;
            }
            if (method_exists($api, 'getInvestmentDetails')) {
                $response = $api->getInvestmentDetails($input['id']);
                echo json_encode($response ?: ['error' => 'Failed to get investment details']);
            } else {
                echo json_encode(['error' => 'Investment details not implemented']);
            }
            break;
        
        case 'verify_referral':
            if (!isset($input['referral_code'])) {
                echo json_encode(['error' => 'Missing referral_code']);
                break;
            }
            $response = $api->verifyReferral($input['referral_code']);
            echo json_encode($response ?: ['exists' => false]);
            break;
        
        default:
            echo json_encode(['error' => 'Unknown action: ' . $action]);
            break;
    }
    
} catch (Exception $e) {
    log_error("AJAX Error: " . $e->getMessage() . " in action: " . ($action ?? 'unknown'), 'ajax_errors.log');
    
    if (ob_get_level()) {
        ob_end_clean();
    }
    
    echo json_encode([
        'error' => 'An error occurred while processing your request',
        'code' => 500,
        'action_required' => 'refresh_page'
    ]);
}
