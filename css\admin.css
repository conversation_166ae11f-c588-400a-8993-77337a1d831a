/* Admin dashboard styles */
.admin-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f8f9fa;
}

/* Admin Header */
.admin-header {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
    padding: 16px 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.admin-header .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.admin-header h1 {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

/* Admin Navigation */
.admin-nav {
    background: white;
    border-bottom: 1px solid #e9ecef;
    padding: 0;
    position: sticky;
    top: 64px;
    z-index: 99;
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.admin-nav::-webkit-scrollbar {
    display: none;
}

.admin-nav .nav-btn {
    background: none;
    border: none;
    padding: 16px 20px;
    cursor: pointer;
    font-weight: 500;
    color: #6c757d;
    transition: all 0.3s ease;
    white-space: nowrap;
    border-bottom: 2px solid transparent;
    font-size: 14px;
}

.admin-nav .nav-btn:hover {
    color: #495057;
    background-color: #f8f9fa;
}

.admin-nav .nav-btn.active {
    color: #e74c3c;
    border-bottom-color: #e74c3c;
}

/* Admin Main */
.admin-main {
    flex: 1;
    padding: 24px 0;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    padding-left: 20px;
    padding-right: 20px;
}

/* Admin Grid */
.admin-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 24px;
}

/* Stats Overview */
.stats-overview {
    background: white;
}

.stats-overview .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.stat-card {
    text-align: center;
    padding: 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    pointer-events: none;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 1;
}

.stat-label {
    font-size: 14px;
    opacity: 0.9;
    font-weight: 500;
    position: relative;
    z-index: 1;
}

/* Activity Stats */
.activity-stats {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
}

.activity-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-label {
    color: #6c757d;
    font-size: 14px;
    font-weight: 500;
}

.activity-value {
    font-weight: 600;
    color: #495057;
    font-size: 16px;
}

/* Transaction Stats */
.transaction-stats {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f1f3f4;
}

.stat-row:last-child {
    border-bottom: none;
}

/* Health Indicators */
.health-indicators {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
}

.health-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
}

.health-item:last-child {
    border-bottom: none;
}

.health-label {
    color: #6c757d;
    font-size: 14px;
    font-weight: 500;
}

.health-status {
    font-weight: 600;
    font-size: 14px;
}

.health-status.healthy {
    color: #28a745;
}

.health-status.warning {
    color: #ffc107;
}

.health-status.error {
    color: #dc3545;
}

/* Users Management */
.users-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    flex-wrap: wrap;
    gap: 16px;
}

.user-filters {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.user-filters select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    background: white;
}

/* Users Table */
.users-table {
    overflow-x: auto;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.users-table table {
    width: 100%;
    min-width: 800px;
}

.users-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    padding: 16px 12px;
    text-align: left;
    border-bottom: 2px solid #dee2e6;
    font-size: 14px;
}

.users-table td {
    padding: 16px 12px;
    border-bottom: 1px solid #f1f3f4;
    font-size: 14px;
    vertical-align: middle;
}

.users-table tr:hover {
    background-color: #f8f9fa;
}

.user-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.user-status.active {
    background-color: #d4edda;
    color: #155724;
}

.user-status.inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.user-admin-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    background-color: #e74c3c;
    color: white;
    text-transform: uppercase;
}

.user-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.user-actions .btn {
    padding: 6px 12px;
    font-size: 12px;
    min-height: 32px;
}

/* Admin Transaction Stats */
.admin-transaction-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
}

.admin-transaction-stats .stat-item {
    text-align: center;
    padding: 16px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.admin-transaction-stats .stat-item span:first-child {
    display: block;
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
    text-transform: uppercase;
    margin-bottom: 8px;
}

.admin-transaction-stats .stat-item span:last-child {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #495057;
}

/* System Info */
.system-info {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
}

.info-row:last-child {
    border-bottom: none;
}

.info-label {
    color: #6c757d;
    font-size: 14px;
    font-weight: 500;
}

.info-value {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.system-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

/* Logs */
.logs-container {
    background: white;
    border-radius: 8px;
    overflow: hidden;
}

.logs-table {
    overflow-x: auto;
}

.logs-table table {
    width: 100%;
    min-width: 900px;
}

.logs-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    padding: 16px 12px;
    text-align: left;
    border-bottom: 2px solid #dee2e6;
    font-size: 14px;
}

.logs-table td {
    padding: 16px 12px;
    border-bottom: 1px solid #f1f3f4;
    font-size: 14px;
    vertical-align: middle;
}

.logs-table tr:hover {
    background-color: #f8f9fa;
}

/* User Action Modal */
.user-info {
    margin-bottom: 24px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;
}

.user-info p {
    margin-bottom: 8px;
    font-size: 14px;
}

.user-info strong {
    color: #495057;
}

.user-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    justify-content: center;
}

/* Responsive design */
@media (max-width: 480px) {
    .admin-header .header-content {
        padding: 0 16px;
    }
    
    .admin-nav {
        padding: 0 16px;
    }
    
    .admin-main {
        padding: 16px;
    }
    
    .admin-nav .nav-btn {
        padding: 14px 16px;
        font-size: 13px;
    }
    
    .header-actions {
        gap: 8px;
    }
    
    .header-actions .btn {
        padding: 6px 12px;
        font-size: 13px;
    }
    
    .stats-overview .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .activity-stats {
        grid-template-columns: 1fr;
    }
    
    .users-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .user-filters {
        width: 100%;
    }
    
    .user-filters select {
        flex: 1;
    }
    
    .admin-transaction-stats {
        grid-template-columns: 1fr;
    }
    
    .system-actions {
        flex-direction: column;
    }
    
    .system-actions .btn {
        width: 100%;
    }
    
    .user-actions {
        flex-direction: column;
    }
    
    .user-actions .btn {
        width: 100%;
    }
}

@media (min-width: 768px) {
    .admin-grid {
        grid-template-columns: 1fr 1fr;
    }
    
    .stats-overview {
        grid-column: 1 / -1;
    }
    
    .activity-stats {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
    
    .admin-transaction-stats {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (min-width: 1024px) {
    .admin-grid {
        grid-template-columns: 2fr 1fr;
    }
    
    .stats-overview {
        grid-column: 1 / -1;
    }
}
