/* Dashboard page styles - Updated for compact responsive design - Version 3.0 */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f8f9fa;
    padding-top: 64px; /* Prevent content from being hidden behind the fixed header */
}

/* ===============================================
   MOBILE-FIRST HEADER STYLES
   =============================================== */

/* Header */
.app-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    width: 100%;
    padding: 12px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 16px;
    gap: 12px;
}

/* Mobile-first brand */
.header-brand {
    flex: 1;
    min-width: 0;
}

.app-header h1 {
    font-size: 1.25rem;
    font-weight: 700;
    margin: 0;
    line-height: 1.2;
}

.brand-subtitle {
    font-size: 0.75rem;
    opacity: 0.8;
    font-weight: 500;
    display: block;
    margin-top: 1px;
    letter-spacing: 0.5px;
}

/* Mobile-first header actions */
.header-actions {
    display: flex;
    gap: 8px;
    align-items: center;
    flex-shrink: 0;
}

.header-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px 12px;
    font-size: 13px;
    min-height: 36px;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.header-btn .btn-text {
    display: none;
}

/* Show text on larger mobile screens */
@media (min-width: 480px) {
    .header-content {
        padding: 0 20px;
    }
    
    .app-header h1 {
        font-size: 1.4rem;
    }
    
    .brand-subtitle {
        font-size: 0.8rem;
    }
    
    .header-btn {
        gap: 6px;
        padding: 8px 14px;
        font-size: 14px;
    }
    
    .header-btn .btn-text {
        display: inline;
    }
}

/* Tablet and desktop styles */
@media (min-width: 768px) {
    .app-header {
        padding: 16px 0;
    }
    
    .header-content {
        gap: 16px;
    }
    
    .app-header h1 {
        font-size: 1.5rem;
    }
    
    .brand-subtitle {
        font-size: 0.85rem;
    }
    
    .header-actions {
        gap: 12px;
    }
    
    .header-btn {
        gap: 8px;
        padding: 10px 16px;
        font-size: 14px;
    }
}

/* Desktop large styles */
@media (min-width: 1024px) {
    .header-content {
        padding: 0 24px;
    }
}

/* Header button variants */
.header-actions .btn {
    padding: 8px 16px;
    font-size: 14px;
    min-height: 36px;
}

/* Navigation */
.app-nav {
    background: white;
    border-bottom: 1px solid #e9ecef;
    padding: 0;
    position: sticky;
    top: 64px;
    z-index: 99;
}

.app-nav {
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.app-nav::-webkit-scrollbar {
    display: none;
}

.nav-btn {
    background: none;
    border: none;
    padding: 16px 20px;
    cursor: pointer;
    font-weight: 500;
    color: #6c757d;
    transition: all 0.3s ease;
    white-space: nowrap;
    border-bottom: 2px solid transparent;
    font-size: 14px;
}

.nav-btn:hover {
    color: #495057;
    background-color: #f8f9fa;
}

.nav-btn.active {
    color: #667eea;
    border-bottom-color: #667eea;
}

/* Main content */
.app-main {
    flex: 1;
    padding: 24px 0;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    padding-left: 20px;
    padding-right: 20px;
}

.tab-content {
    display: none;
}

.tab-content {
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s ease;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease;
    opacity: 1;
    transform: translateY(0);
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Dashboard grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 24px;
}

/* Balance card */
.balance-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.balance-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    pointer-events: none;
}

.balance-card h3 {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 16px;
    font-size: 1.1rem;
}

.balance-amount {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.balance-currency {
    font-size: 1.2rem;
    opacity: 0.9;
    font-weight: 500;
}

/* Stats card */
.stats-card {
    background: white;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
}

/* Stat card styling */
.stat-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 16px;
    transition: box-shadow 0.3s ease;
}

.stat-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    flex-shrink: 0;
    width: 48px;
    height: 48px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.stat-icon svg {
    width: 24px;
    height: 24px;
}

.stat-info {
    flex: 1;
    min-width: 0;
}

.stat-info h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 4px 0;
    line-height: 1.2;
}

.stat-info p {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0;
    font-weight: 500;
}

/* Additional dashboard improvements */
.stats-grid .stat-card:nth-child(1) .stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-grid .stat-card:nth-child(2) .stat-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-grid .stat-card:nth-child(3) .stat-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-grid .stat-card:nth-child(4) .stat-icon {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

/* Loading states */
.stat-info h3:empty::after {
    content: "Loading...";
    color: #6c757d;
    font-weight: 400;
    font-size: 1rem;
}

#recentTransactionsList:empty::after {
    content: "Loading recent transactions...";
    color: #6c757d;
    font-style: italic;
    display: block;
    text-align: center;
    padding: 20px;
}

/* Recent transactions */
.recent-transactions {
    background: white;
}

.transaction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
    transition: all 0.2s ease;
    border-radius: 0;
    overflow: hidden;
    word-wrap: break-word;
    min-height: 48px;
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-item:hover {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding-left: 8px;
    padding-right: 8px;
}

.transaction-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.transaction-type {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.transaction-date {
    color: #6c757d;
    font-size: 12px;
    margin-top: 2px;
}

.transaction-amount {
    font-weight: 600;
    font-size: 14px;
    text-align: right;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.transaction-amount.positive {
    color: #28a745;
}

.transaction-amount.negative {
    color: #dc3545;
}

/* Wallet section */
.wallet-section {
    display: grid;
    grid-template-columns: 1fr;
    gap: 24px;
}

.wallet-info {
    display: grid;
    gap: 20px;
}

.wallet-field {
    margin-bottom: 20px;
}

.wallet-field label {
    display: block;
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 8px;
    font-weight: 500;
}

.wallet-address-input,
.wallet-balance-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    background: #f8f9fa;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    color: #495057;
    min-height: 44px;
    box-sizing: border-box;
}

.wallet-address-input:focus,
.wallet-balance-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.address-display,
.balance-display {
    display: flex;
    align-items: center;
    gap: 12px;
}

.address-display .wallet-address-input,
.balance-display .wallet-balance-input {
    flex: 1;
}

/* Transactions section */
.transactions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    flex-wrap: wrap;
    gap: 16px;
}

.transaction-filters {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
}

.transaction-filters select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
}

.transaction-list {
    background: white;
    border-radius: 8px;
    overflow: hidden;
}

.transaction-row {
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 16px;
    padding: 16px;
    border-bottom: 1px solid #f1f3f4;
    align-items: center;
}

.transaction-row:last-child {
    border-bottom: none;
}

.transaction-details {
    min-width: 0;
}

.transaction-hash {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: #6c757d;
    word-break: break-all;
}

.transaction-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
}

.transaction-status.confirmed {
    background-color: #d4edda;
    color: #155724;
}

.transaction-status.pending {
    background-color: #fff3cd;
    color: #856404;
}

.transaction-status.failed {
    background-color: #f8d7da;
    color: #721c24;
}

/* Deposit section */
.deposit-info {
    text-align: center;
    margin-bottom: 32px;
}

.deposit-info > p {
    font-size: 1.1rem;
    color: #495057;
    margin-bottom: 24px;
    text-align: center;
}

.deposit-address {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 24px;
    border-radius: 12px;
    margin: 24px 0;
    border: 2px solid #dee2e6;
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.address-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.address-container label {
    font-weight: 600;
    color: #495057;
    font-size: 1rem;
    margin-bottom: 8px;
}

.address-display-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: center;
}

.deposit-address-text {
    background: white;
    padding: 16px 20px;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
    word-break: break-all;
    border: 1px solid #ced4da;
    min-height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    box-sizing: border-box;
    text-align: center;
    color: #495057;
}

.deposit-warnings {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 20px;
    margin-top: 24px;
}

.deposit-warnings h4 {
    color: #856404;
    margin-bottom: 12px;
    font-size: 1.1rem;
    font-weight: 600;
}

.deposit-warnings ul {
    margin: 0;
    padding-left: 20px;
    color: #856404;
}

.deposit-warnings li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.deposit-qr {
    margin-top: 24px;
    text-align: center;
    padding: 20px;
    background: white;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.manual-deposit {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 24px;
    margin-top: 32px;
    border: 1px solid #e9ecef;
}

.manual-deposit h4 {
    margin-bottom: 16px;
    color: #495057;
    font-size: 1.2rem;
    font-weight: 600;
}

.manual-deposit p {
    color: #6c757d;
    line-height: 1.6;
    margin-bottom: 16px;
}

.support-info {
    background: #e7f3ff;
    border: 1px solid #b8daff;
    border-radius: 6px;
    padding: 16px;
    margin-top: 16px;
}

.support-info p {
    color: #004085;
    margin: 0;
    font-size: 0.9rem;
}

/* Copy button styling */
#copyDepositBtn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

#copyDepositBtn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

/* Sticky Footer Menu */
.footer-menu {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #e9ecef;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: block !important; /* Always visible on all screen sizes */
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    transform: translateY(0) !important; /* Always shown */
    transition: transform 0.3s ease;
}

.footer-menu.show {
    transform: translateY(0);
}

.footer-nav {
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 12px 8px 8px 8px;
    max-width: 100%;
    background: rgba(255, 255, 255, 0.95);
}

.footer-btn {
    background: none;
    border: none;
    padding: 8px 4px;
    cursor: pointer;
    color: #6c757d;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    font-size: 10px;
    font-weight: 500;
    min-width: 60px;
    text-align: center;
    border-radius: 8px;
    position: relative;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}

.footer-btn:hover,
.footer-btn:focus {
    color: #495057;
    background-color: rgba(102, 126, 234, 0.1);
    outline: none;
}

.footer-btn:active {
    transform: scale(0.95);
}

.footer-btn.active {
    color: #667eea;
}

.footer-btn.active::before {
    content: '';
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 24px;
    height: 3px;
    background: #667eea;
    border-radius: 2px;
}

.footer-icon {
    width: 22px;
    height: 22px;
    transition: all 0.3s ease;
}

.footer-btn.active .footer-icon {
    transform: scale(1.1);
}

.footer-btn span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    margin-top: 2px;
}

/* Add bottom padding to main content when footer is visible */
.app-container.with-footer {
    padding-bottom: 70px;
}

/* Safe area insets for modern devices (iPhone X, etc.) */
@supports (padding: max(0px)) {
    .footer-menu {
        padding-bottom: max(8px, env(safe-area-inset-bottom));
    }
    
    .app-container {
        padding-bottom: calc(70px + max(0px, env(safe-area-inset-bottom)));
    }
}

/* Dark mode support for footer */
@media (prefers-color-scheme: dark) {
    .footer-menu {
        background: #1a1a1a;
        border-top-color: #333;
    }
    
    .footer-nav {
        background: rgba(26, 26, 26, 0.95);
    }
    
    .footer-btn {
        color: #a0a0a0;
    }
    
    .footer-btn:hover {
        color: #ffffff;
        background-color: rgba(102, 126, 234, 0.2);
    }
    
    .footer-btn.active {
        color: #667eea;
    }
}

/* Desktop and all screen sizes - always show footer menu */
@media (min-width: 769px) {
    .footer-menu {
        display: block !important;
        transform: translateY(0) !important;
    }
    
    /* Ensure navigation is visible on desktop */
    .app-nav {
        display: flex;
    }
    
    /* Maintain bottom padding for footer on desktop */
    .app-container {
        padding-bottom: 80px;
    }
    
    .app-main {
        padding-bottom: 80px;
    }
}

/* Desktop and all screen sizes - always show footer menu */
@media (min-width: 769px) {
    .footer-menu {
        display: block !important;
        transform: translateY(0) !important;
        opacity: 1 !important;
        visibility: visible !important;
    }
    
    /* Ensure navigation is visible on desktop */
    .app-nav {
        display: flex;
    }
    
    /* Maintain bottom padding for footer on desktop */
    .app-container {
        padding-bottom: 80px;
    }
    
    .app-main {
        padding-bottom: 80px;
    }
}

/* Responsive design */
@media (max-width: 768px) {
    /* Hide regular navigation on mobile */
    .app-nav {
        display: none;
    }
    
    /* Show footer menu on mobile */
    .footer-menu {
        display: block !important;
        transform: translateY(0);
    }
    
    /* Add padding to prevent content from being hidden behind footer */
    .app-container {
        padding-bottom: 70px;
    }
    
    .header-content {
        padding: 0 16px;
    }
    
    .app-main {
        padding: 16px;
        padding-bottom: 80px; /* Extra padding for footer */
    }
    
    .balance-amount {
        font-size: 2rem;
    }
    
    .wallet-actions {
        flex-direction: column;
    }
    
    .wallet-actions .btn {
        width: 100%;
    }
    
    .transactions-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .transaction-filters {
        width: 100%;
    }
    
    .transaction-filters select {
        flex: 1;
    }
}

@media (max-width: 480px) {
    .header-content {
        padding: 0 12px;
    }
    
    .app-main {
        padding: 12px;
    }
    
    .header-actions {
        gap: 6px;
    }
    
    .header-actions .btn {
        padding: 6px 10px;
        font-size: 12px;
    }
    
    .footer-btn {
        padding: 6px 2px;
        font-size: 9px;
        min-width: 50px;
    }
    
    .footer-icon {
        width: 18px;
        height: 18px;
    }
}

@media (min-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr 1fr;
    }
    
    .balance-card {
        grid-column: 1 / -1;
    }
      .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
    
    .wallet-section {
        grid-template-columns: 1fr 1fr;
    }
    
    .manual-deposit {
        grid-column: 1 / -1;
    }
    
    .transaction-row {
        grid-template-columns: auto 1fr auto auto;
    }
}

@media (min-width: 1024px) {
    .dashboard-grid {
        grid-template-columns: 2fr 1fr;
    }
    
    .balance-card {
        grid-column: 1 / -1;
    }
    
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 20px;
    }
    
    .recent-transactions {
        grid-column: 1;
        grid-row: 2;
    }
    
    .stats-card {
        grid-column: 2;
        grid-row: 2;
    }
}

/* Profile Page Styles */
.profile-page {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
    min-height: calc(100vh - 200px);
}

.profile-card {
    margin-bottom: 24px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border: 1px solid #e9ecef;
}

.profile-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 32px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
}

.profile-header-content h2 {
    margin: 0 0 8px 0;
    font-size: 2rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 12px;
}

.profile-header-content p {
    margin: 0;
    opacity: 0.9;
    font-size: 1.1rem;
}

.profile-header-status {
    flex-shrink: 0;
}

.profile-section {
    padding: 32px;
    border-bottom: 1px solid #f1f3f4;
    position: relative;
}

.profile-section:last-child {
    border-bottom: none;
}

.profile-section h3 {
    margin: 0 0 24px 0;
    color: #2c3e50;
    font-size: 1.4rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 12px;
    padding-bottom: 12px;
    border-bottom: 2px solid #f1f3f4;
}

/* Account Information Section */
.account-info-section {
    background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
}

.profile-info {
    display: grid;
    gap: 20px;
}

.profile-field {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: white;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.profile-field:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.profile-field label {
    font-weight: 600;
    color: #495057;
    margin: 0;
    font-size: 1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.profile-field .field-value {
    color: #2c3e50;
    font-weight: 500;
    font-size: 1rem;
}

/* Status Badge Enhanced */
.status-badge {
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.status-badge:hover {
    transform: scale(1.05);
}

.status-active {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border: 1px solid #c3e6cb;
}

/* Password Section */
.password-section {
    background: linear-gradient(135deg, #fff8f0 0%, #ffffff 100%);
}

.form-container {
    background: white;
    border-radius: 12px;
    padding: 24px;
    border: 1px solid #e9ecef;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr;
    gap: 20px;
    margin-bottom: 24px;
}

.form-group {
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
    font-size: 0.95rem;
}

.form-control {
    width: 100%;
    padding: 14px 16px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #fafafa;
    box-sizing: border-box;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Form validation states */
.form-control.error {
    border-color: #dc3545;
    background-color: #fff5f5;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.form-control.success {
    border-color: #28a745;
    background-color: #f0fff4;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1);
}

.form-control.error:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2);
}

.form-control.success:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.2);
}

.field-help {
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 6px;
    font-style: italic;
}

.password-requirements {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin: 24px 0;
    border-left: 4px solid #667eea;
}

.password-requirements h4 {
    margin: 0 0 12px 0;
    color: #495057;
    font-size: 1rem;
    font-weight: 600;
}

.password-requirements ul {
    margin: 0;
    padding-left: 20px;
    color: #6c757d;
}

.password-requirements li {
    margin-bottom: 8px;
    line-height: 1.5;
}

/* Security Section */
.security-section {
    background: linear-gradient(135deg, #f0fff4 0%, #ffffff 100%);
}

.security-card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    border: 1px solid #e9ecef;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.security-icon {
    font-size: 2rem;
    flex-shrink: 0;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 16px rgba(40, 167, 69, 0.2);
}

.security-content h4 {
    margin: 0 0 12px 0;
    color: #2c3e50;
    font-size: 1.2rem;
    font-weight: 600;
}

.security-content p {
    margin: 0 0 16px 0;
    color: #6c757d;
    line-height: 1.6;
}

.security-list {
    margin: 0;
    padding-left: 20px;
    color: #495057;
}

.security-list li {
    margin-bottom: 8px;
    line-height: 1.5;
    font-weight: 500;
}

/* ========================================
   MOBILE RESPONSIVE FIXES FOR SECURITY SECTION
   ======================================== */

/* Mobile Small (≤480px) - Security Tips Mobile Optimization */
@media (max-width: 480px) {
    .security-card {
        flex-direction: column;
        gap: 16px;
        padding: 20px 16px;
        align-items: center;
        text-align: center;
    }
    
    .security-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
        margin-bottom: 4px;
    }
    
    .security-content {
        width: 100%;
    }
    
    .security-content h4 {
        font-size: 1.1rem;
        margin-bottom: 10px;
    }
    
    .security-content p {
        font-size: 0.9rem;
        margin-bottom: 14px;
        text-align: center;
    }
    
    .security-list {
        text-align: left;
        padding-left: 16px;
        margin: 0;
    }
    
    .security-list li {
        margin-bottom: 10px;
        font-size: 0.9rem;
        line-height: 1.6;
    }
}

/* Mobile Medium (481px - 768px) - Security Tips Tablet Optimization */
@media (min-width: 481px) and (max-width: 768px) {
    .security-card {
        gap: 18px;
        padding: 22px 20px;
    }
    
    .security-icon {
        width: 55px;
        height: 55px;
        font-size: 1.7rem;
    }
    
    .security-content h4 {
        font-size: 1.15rem;
    }
    
    .security-content p {
        font-size: 0.95rem;
    }
    
    .security-list {
        padding-left: 18px;
    }
    
    .security-list li {
        margin-bottom: 9px;
        font-size: 0.95rem;
    }
}

/* Tablet and Small Desktop (769px - 1023px) - Enhanced Layout */
@media (min-width: 769px) and (max-width: 1023px) {
    .security-card {
        gap: 20px;
        padding: 24px 22px;
    }
    
    .security-icon {
        width: 58px;
        height: 58px;
        font-size: 1.8rem;
    }
    
    .security-content h4 {
        font-size: 1.18rem;
    }
}

/* Large Desktop (≥1024px) - Enhanced Visual Appeal */
@media (min-width: 1024px) {
    .security-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
    }
    
    .security-icon:hover {
        transform: scale(1.05);
        transition: all 0.3s ease;
    }
}

/* ========================================
   ENHANCED DASHBOARD COMPACT & RESPONSIVE STYLES
   ======================================== */

/* Dashboard Content - Compact Layout */
.dashboard-content {
    padding: 16px 0;
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    padding-left: 16px;
    padding-right: 16px;
    min-height: calc(100vh - 140px);
    box-sizing: border-box;
}

/* Page Header - Compact */
.page-header {
    margin-bottom: 20px;
    text-align: center;
}

.page-header h2 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 6px 0;
}

.page-header p {
    font-size: 0.95rem;
    color: #6c757d;
    margin: 0 0 12px 0;
}

/* Dashboard-specific stat cards with enhanced specificity */
.dashboard-content .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 12px;
    margin-bottom: 20px;
}

.dashboard-content .stat-card {
    background: white;
    border-radius: 10px;
    padding: 14px !important; /* Override any base styles */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 10px !important; /* Compact gap */
    transition: all 0.2s ease;
}

.dashboard-content .stat-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.dashboard-content .stat-icon {
    flex-shrink: 0;
    width: 36px !important; /* Compact size */
    height: 36px !important; /* Compact size */
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.dashboard-content .stat-icon svg {
    width: 18px !important; /* Compact size */
    height: 18px !important; /* Compact size */
}

.dashboard-content .stat-info h3 {
    font-size: 1.2rem !important; /* Compact font size */
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 2px 0 !important;
    line-height: 1.2;
}

.dashboard-content .stat-info p {
    font-size: 0.75rem !important; /* Compact font size */
    color: #6c757d;
    margin: 0 !important;
    font-weight: 500;
}

/* Card Layouts - Compact & Responsive */
/* Dashboard-specific card overrides for compact layout */
.dashboard-content .card {
    background: white;
    border-radius: 12px;
    padding: 16px !important; /* Override base style.css */
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    margin-bottom: 16px !important; /* Override base style.css */
    transition: all 0.2s ease;
}

.dashboard-content .card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.dashboard-content .card h3 {
    font-size: 1.1rem !important; /* Override base style.css */
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 12px 0 !important; /* Override base style.css */
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Better visual hierarchy */
.card h3::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
    margin-right: 8px;
    vertical-align: middle;
}

/* Active Investments - Enhanced Responsive Design */
.card:has(#activeInvestmentsList) {
    padding: 18px;
}

.investment-summary-card {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
    border-radius: 10px;
    padding: 14px;
    margin-bottom: 14px;
    border: 1px solid #e1e7ff;
    box-shadow: 0 2px 6px rgba(102, 126, 234, 0.08);
}

.investment-summary-card h4 {
    margin: 0 0 10px 0 !important;
    font-size: 1.05rem !important;
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 6px;
}

/* Summary Stats - Responsive Grid */
.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
    gap: 10px;
}

.summary-stat {
    text-align: center;
    padding: 8px 6px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.summary-stat:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.summary-stat-value {
    font-size: 0.95rem;
    font-weight: 700;
    color: #28a745;
    margin-bottom: 2px;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.summary-stat-label {
    font-size: 0.7rem;
    color: #6c757d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

/* Investment Grid - Responsive Layout */
.investment-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
    margin-top: 12px;
}

/* Investment Items - Enhanced Compact Design */
.investment-item {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 10px;
    padding: 14px;
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    cursor: pointer;
    overflow: hidden;
}

.investment-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 10px 10px 0 0;
}

.investment-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.15);
    border-color: #667eea;
    z-index: 2;
}

.investment-status {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

.investment-status.active {
    background: rgba(40, 167, 69, 0.2);
    color: #1e5b2a;
    border: 1px solid rgba(40, 167, 69, 0.4);
}

.investment-status.completed {
    background: rgba(102, 126, 234, 0.2);
    color: #4c5db7;
    border: 1px solid rgba(102, 126, 234, 0.4);
}

/* Investment Info - Responsive Layout */
.investment-info {
    padding-right: 65px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.investment-details {
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.investment-plan {
    font-size: 0.95rem;
    font-weight: 600;
    color: #495057;
}

.investment-amount {
    font-size: 0.9rem;
    color: #28a745;
    font-weight: 600;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.performance-indicator {
    font-size: 0.75rem;
    color: #667eea;
    font-weight: 500;
    padding: 2px 6px;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 8px;
    display: inline-block;
    width: fit-content;
}

.investment-earnings {
    display: flex;
    flex-direction: column;
    gap: 2px;
    font-size: 0.8rem;
}

.daily-return {
    color: #28a745;
    font-weight: 600;
}

.total-earned {
    color: #6c757d;
}

/* Investment Progress - Enhanced Design */
.investment-progress {
    margin-top: 10px;
}

.progress-bar {
    width: 100%;
    height: 5px;
    background-color: #e9ecef;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 6px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 3px;
    transition: width 0.4s ease;
}

.progress-text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    color: #6c757d;
}

/* Recent Transactions - Enhanced Responsive Design */
.card:has(#recentTransactionsList) {
    padding: 18px;
}

/* Transaction Items - Improved Layout */
.transaction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
    transition: all 0.2s ease;
    border-radius: 0;
    overflow: hidden;
    word-wrap: break-word;
    min-height: 48px;
}

.transaction-item:last-child {
    border-bottom: none;
}

.transaction-item:hover {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding-left: 8px;
    padding-right: 8px;
}

.transaction-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.transaction-type {
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.transaction-date {
    color: #6c757d;
    font-size: 12px;
    margin-top: 2px;
}

.transaction-amount {
    font-weight: 600;
    font-size: 14px;
    text-align: right;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.transaction-amount.positive {
    color: #28a745;
}

.transaction-amount.negative {
    color: #dc3545;
}

/* Card Footer - Responsive Design */
.card-footer {
    border-top: 1px solid #f1f3f4;
    padding: 16px 0 0 0;
    margin-top: 16px;
    display: flex;
    gap: 10px;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.card-footer .btn {
    padding: 8px 16px;
    font-size: 0.85rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
    min-height: 44px;
    min-width: 44px;
}

.card-footer .btn-primary {
    background: #667eea;
    color: white;
    border: 1px solid #667eea;
}

.card-footer .btn-outline {
    background: transparent;
    color: #667eea;
    border: 1px solid #667eea;
}

.card-footer .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
}

.card-footer .btn:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* No Data States - Enhanced Design */
.no-data {
    text-align: center;
    padding: 24px 16px;
    color: #6c757d;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px dashed #e9ecef;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
}

.no-data::before {
    content: '📭';
    font-size: 2rem;
    display: block;
    margin-bottom: 8px;
}

.no-data h4 {
    font-size: 1.1rem;
    color: #495057;
    margin-bottom: 6px;
    font-weight: 600;
}

.no-data p {
    font-size: 0.85rem;
    margin: 0;
}

/* Loading States - Enhanced Animation */
.loading-investments {
    text-align: center;
    padding: 24px 16px;
}

.loading-spinner-container {
    margin-bottom: 12px;
}

.loading-spinner-ring {
    display: inline-block;
    width: 24px;
    height: 24px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0;
}

/* Enhanced Animations & Micro-Interactions */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulseGlow {
    0%, 100% {
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
    }
    50% {
        box-shadow: 0 4px 20px rgba(102, 126, 234, 0.2);
    }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

@keyframes loadingDots {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
}

/* Apply entrance animations */
.card {
    animation: fadeInUp 0.6s ease-out;
}

.stat-card {
    animation: slideInRight 0.5s ease-out;
    animation-fill-mode: both;
    will-change: transform, box-shadow;
}

.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }

.investment-item.pulse {
    animation: pulseGlow 2s infinite;
}

.investment-item.new-investment {
    animation: fadeInUp 0.6s ease-out;
}

.investment-item {
    will-change: transform, box-shadow;
    position: relative;
}

.transaction-item {
    will-change: background-color, padding;
}

.loading-dots::after {
    content: '';
    animation: loadingDots 1.5s infinite;
}

/* Improved accessibility */
.investment-item:focus,
.card:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

.investment-item[tabindex]:focus,
.transaction-item[tabindex]:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
    z-index: 1;
    position: relative;
}

/* Better loading states */
.investment-summary-card.loading {
    background: linear-gradient(90deg, #f8f9ff, #e9ecef, #f8f9ff);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

.transaction-item.loading {
    opacity: 0.6;
    pointer-events: none;
}

.transaction-item.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: shimmer 1.5s infinite;
}

/* Smooth scroll behavior */
html {
    scroll-behavior: smooth;
}

/* ========================================
   RESPONSIVE BREAKPOINTS
   ======================================== */

/* Mobile First - Small Screens (up to 480px) */
@media (max-width: 480px) {
    .dashboard-content {
        padding: 12px 12px 80px 12px;
    }
    
    .page-header h2 {
        font-size: 1.3rem;
    }
    
    .page-header p {
        font-size: 0.9rem;
    }
    
    /* Stats Grid - Mobile Stack */
    .stats-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    .stat-card {
        padding: 12px;
        gap: 10px;
    }
    
    .stat-icon {
        width: 36px;
        height: 36px;
    }
    
    .stat-icon svg {
        width: 18px;
        height: 18px;
    }
    
    .stat-info h3 {
        font-size: 1.2rem;
    }
    
    .stat-info p {
        font-size: 0.75rem;
    }
    
    /* Cards - Ultra Compact */
    .card {
        padding: 14px;
        margin-bottom: 14px;
        border-radius: 10px;
    }
    
    .card h3 {
        font-size: 1.1rem;
        margin-bottom: 12px;
    }
    
    /* Investment Summary - Mobile Optimized */
    .investment-summary-card {
        padding: 10px;
        margin-bottom: 10px;
    }
    
    .investment-summary-card h4 {
        font-size: 0.95rem !important;
        margin-bottom: 8px !important;
    }
    
    .summary-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 6px;
    }
    
    .summary-stat {
        padding: 6px 4px;
    }
    
    .summary-stat-value {
        font-size: 0.8rem;
    }
    
    .summary-stat-label {
        font-size: 0.6rem;
    }
    
    /* Investment Items - Mobile Compact */
    .investment-item {
        padding: 10px;
        border-radius: 8px;
    }
    
    .investment-info {
        padding-right: 55px;
    }
    
    .investment-plan {
        font-size: 0.85rem;
    }
    
    .investment-amount {
        font-size: 0.8rem;
    }
}

/* Extra Small Screens */
@media (max-width: 360px) {
    .dashboard-content {
        padding: 8px 8px 80px 8px;
    }
    
    .card {
        padding: 12px;
        margin-bottom: 12px;
    }
    
    .card h3 {
        font-size: 1rem;
        margin-bottom: 10px;
    }
    
    .stats-grid {
        gap: 8px;
    }
    
    .stat-card {
        padding: 10px;
        gap: 8px;
    }
    
    .stat-icon {
        width: 32px;
        height: 32px;
    }
    
    .stat-icon svg {
        width: 16px;
        height: 16px;
    }
    
    .stat-info h3 {
        font-size: 1rem;
    }
    
    .stat-info p {
        font-size: 0.7rem;
    }
    
    .investment-summary-card {
        padding: 8px;
    }
    
    .summary-stats {
        gap: 4px;
    }
    
    .summary-stat {
        padding: 4px 2px;
    }
    
    .summary-stat-value {
        font-size: 0.75rem;
    }
    
    .summary-stat-label {
        font-size: 0.55rem;
    }
    
    .investment-item {
        padding: 8px;
    }
    
    .investment-info {
        padding-right: 45px;
    }
    
    .investment-plan {
        font-size: 0.8rem;
    }
    
    .investment-amount {
        font-size: 0.75rem;
    }
}

/* Tablet - Medium Screens (481px to 768px) */
@media (min-width: 481px) and (max-width: 768px) {
    .dashboard-content {
        padding: 16px 16px 80px 16px;
    }
    
    /* Stats Grid - 2 Column Layout */
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 14px;
    }
    
    .stat-card {
        padding: 16px;
    }
    
    /* Investment Grid - Single Column */
    .investment-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }
    
    /* Summary Stats - 4 Column Layout */
    .summary-stats {
        grid-template-columns: repeat(4, 1fr);
        gap: 8px;
    }
    
    /* Card Footer - Horizontal Layout */
    .card-footer {
        flex-direction: row;
        justify-content: center;
    }
    
    .card-footer .btn {
        flex: 1;
        max-width: 160px;
    }
}

/* Desktop - Large Screens (769px and up) */
@media (min-width: 769px) {
    .dashboard-content {
        padding: 24px 20px 80px 20px;
    }
    
    .transactions-page {
        padding: 24px 20px 80px 20px;
    }
    
    .transactions-page .page-header h2 {
        font-size: 1.8rem;
    }
    
    .transactions-page .page-header p {
        font-size: 1.1rem;
    }
    
    .transaction-filters {
        padding: 20px;
    }
    
    .filter-row {
        grid-template-columns: repeat(4, 1fr);
        gap: 16px;
    }
    
    .filter-group select {
        min-height: 44px;
        font-size: 1rem;
    }
    
    .filter-actions {
        flex-direction: row;
        gap: 12px;
        margin-top: 16px;
    }
    
    .filter-actions .btn {
        flex: none;
        min-width: 120px;
        font-size: 0.9rem;
        min-height: 44px;
    }
    
    .transaction-header {
        padding: 16px 20px;
    }
    
    .transaction-details {
        padding: 20px;
    }
    
    .transaction-amount {
        font-size: 1.3rem;
        padding: 12px;
    }
    
    .transaction-info {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }
    
    .transaction-info .label {
        font-size: 0.9rem;
        min-width: 60px;
    }
    
    .transaction-info .hash,
    .transaction-info .date,
    .transaction-info .address,
    .transaction-info .note {
        font-size: 0.9rem;
    }
    
    .pagination-container {
        padding: 20px;
    }
    
    .pagination-controls .btn {
        min-height: 40px;
        font-size: 0.9rem;
    }
}

/* Extra Large Screens (1200px and up) */
@media (min-width: 1200px) {
    .dashboard-content {
        padding: 32px 24px 80px 24px;
    }
    
    .transactions-page {
        padding: 32px 24px 80px 24px;
    }
    
    .transaction-info {
        grid-template-columns: repeat(3, 1fr);
    }
    
    /* Enhanced hover effects for large screens */
    .transaction-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
    }
    
    .filter-actions .btn:hover {
        transform: translateY(-1px);
    }
}

/* Landscape orientation optimizations */
@media (orientation: landscape) and (max-height: 500px) {
    .transactions-page {
        padding-top: 12px;
        padding-bottom: 60px;
    }
    
    .transactions-page .page-header {
        margin-bottom: 16px;
        padding-bottom: 12px;
    }
    
    #transactionsList {
        max-height: 60vh;
    }
}

/* High-DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .transaction-type,
    .transaction-status {
        border-radius: 14px;
    }
    
    .filter-group select {
        border-width: 1px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .transaction-filters {
        background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);
        border-color: #333;
    }
    
    .filter-group select {
        background: #2a2a2a;
        color: #fff;
        border-color: #444;
    }
    
    .transaction-item {
        background: #1a1a1a;
        border-color: #333;
    }
    
    .transaction-header {
        background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);
        border-color: #333;
    }
    
    .pagination-container {
        background: linear-gradient(135deg, #2a2a2a 0%, #1f1f1f 100%);
        border-color: #333;
    }
}

/* Print styles */
@media print {
    .transaction-filters,
    .pagination-container,
    .filter-actions {
        display: none !important;
    }
    
    .transaction-item {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
        margin-bottom: 8px;
    }
    
    .transaction-header {
        background: none !important;
        padding: 8px;
    }
    
    .transaction-details {
        padding: 8px;
    }
}

/* ===============================================
   MAKE INVESTMENT PAGE STYLES
   Following invest.php page patterns
   =============================================== */

/* Make Investment Page Container */
.make-investment-page {
    max-width: 1200px;
    margin: 0 auto;
    padding: 16px;
    background: #f8f9fa;
    min-height: calc(100vh - 140px);
}

/* Page Header - Same as invest page */
.make-investment-page .page-header {
    text-align: center;
    margin-bottom: 24px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.make-investment-page .page-header h2 {
    margin: 0 0 8px 0;
    font-size: 1.5rem;
    font-weight: 700;
}

.make-investment-page .page-header p {
    margin: 0;
    opacity: 0.9;
    font-size: 0.9rem;
}

/* Balance Overview - Using same styling as invest page */
.make-investment-page .balance-overview {
    margin-bottom: 24px;
}

.make-investment-page .balance-overview .balance-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.make-investment-page .balance-overview .balance-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    pointer-events: none;
}

.make-investment-page .balance-overview .balance-card h3 {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 16px;
    font-size: 1.1rem;
}

.make-investment-page .balance-overview .balance-amount {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.make-investment-page .balance-overview .balance-currency {
    font-size: 1.2rem;
    opacity: 0.9;
    font-weight: 500;
}

/* Investment Plans Section */
.investment-plans-section {
    margin-bottom: 24px;
}

.investment-plans-section h3 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 1.3rem;
    font-weight: 600;
    text-align: center;
}

.investment-plans {
    display: grid;
    grid-template-columns: 1fr;
    gap: 16px;
}

.investment-plan {
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.investment-plan:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.15);
}

.investment-plan.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.2);
}

.plan-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.plan-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
}

.plan-rate {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 6px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.plan-details {
    display: grid;
    grid-template-columns: 1fr;
    gap: 10px;
    font-size: 0.95rem;
}

.plan-detail {
    display: flex;
    justify-content: space-between;
    padding: 6px 0;
    border-bottom: 1px solid #f0f0f0;
}

.plan-detail:last-child {
    border-bottom: none;
}

.plan-detail-label {
    color: #6c757d;
    font-weight: 500;
}

.plan-detail-value {
    color: #495057;
    font-weight: 600;
}

/* Investment Form - Following card pattern */
.make-investment-page .card {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.make-investment-page .card h3 {
    margin-bottom: 20px;
    color: #2c3e50;
    font-size: 1.2rem;
    font-weight: 600;
}

/* Plan Summary Card */
.selected-plan-summary {
    margin-bottom: 20px;
}

.plan-summary-card {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
    border: 2px solid #667eea;
    border-radius: 12px;
    padding: 16px;
}

.plan-summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.plan-summary-header h4 {
    margin: 0;
    color: #667eea;
    font-size: 1.1rem;
    font-weight: 600;
}

.selected-plan-rate {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 0.8rem;
    font-weight: 600;
}

.plan-summary-details {
    display: grid;
    grid-template-columns: 1fr;
    gap: 8px;
    font-size: 0.9rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    padding: 4px 0;
}

.detail-label {
    color: #6c757d;
    font-weight: 500;
}

.detail-value {
    color: #495057;
    font-weight: 600;
}

/* Balance Info Card */
.balance-info {
    margin-bottom: 20px;
}

.balance-info-card {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 16px;
    text-align: center;
}



.balance-info-header h4 {
    margin: 0 0 8px 0;
    color: #495057;
    font-size: 1rem;
    font-weight: 500;
}

.balance-info-amount {
    font-size: 1.4rem;
    font-weight: 700;
    color: #28a745;
    margin-bottom: 4px;
}

.balance-status {
    font-size: 0.85rem;
    color: #6c757d;
}

/* Form styling */
.investment-form-container .form-group {
    margin-bottom: 20px;
}

.investment-form-container .form-group label {
    display: block;
    margin-bottom: 6px;
    font-weight: 500;
    color: #555;
    font-size: 0.95rem;
}

.investment-form-container .form-group input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 16px;
    transition: border-color 0.3s ease;
    background-color: white;
}

.investment-form-container .form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.25);
}

.input-help {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 4px;
}

.amount-validation {
    font-size: 0.8rem;
    margin-top: 4px;
}

/* Investment Summary */
.investment-summary {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
}

.investment-summary h4 {
    margin: 0 0 16px 0;
    color: #495057;
    font-size: 1rem;
    font-weight: 600;
}

.summary-details {
    display: grid;
    gap: 8px;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
    font-size: 0.95rem;
}

.summary-row:last-child {
    border-bottom: none;
    font-weight: 600;
    color: #28a745;
    font-size: 1rem;
}

/* Form Actions */
.form-actions {
    display: grid;
    gap: 12px;
}

/* Warning Cards */
.insufficient-balance {
    text-align: center;
    padding: 20px;
}

.warning-card {
    background: #fff3cd;
    border: 1px solid #ffeeba;
    border-radius: 12px;
    padding: 30px;
    max-width: 500px;
    margin: 0 auto;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.warning-card.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
}

.warning-icon {
    margin-bottom: 20px;
}

.warning-card h4 {
    color: #856404;
    margin: 0 0 15px 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.warning-card.success h4 {
    color: #155724;
}

.warning-card p {
    color: #856404;
    margin: 0 0 15px 0;
    font-size: 1rem;
    line-height: 1.6;
}

.warning-card.success p {
    color: #155724;
}

.balance-needed {
    background: rgba(255, 193, 7, 0.1);
    border-radius: 8px;
    padding: 12px;
    margin: 15px 0;
}

.needed-amount {
    color: #856404;
    font-weight: 600;
    font-size: 1rem;
}

.investment-benefits {
    text-align: left;
    margin: 20px 0;
    padding: 20px;
    background: rgba(40, 167, 69, 0.05);
    border-radius: 8px;
    border-left: 4px solid #28a745;
}

.investment-benefits h5 {
    color: #155724;
    margin: 0 0 12px 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.investment-benefits ul {
    margin: 0;
    padding: 0 0 0 20px;
    color: #155724;
}

.investment-benefits li {
    margin-bottom: 8px;
    font-size: 0.95rem;
    line-height: 1.4;
}

.warning-actions {
    display: grid;
    gap: 12px;
    margin-top: 25px;
}

/* Error Container Styles */
.error-container {
    text-align: center;
    padding: 20px;
}

.error-card {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 12px;
    padding: 30px;
    max-width: 500px;
    margin: 0 auto;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.error-icon {
    margin-bottom: 20px;
}

.error-card h4 {
    color: #721c24;
    margin: 0 0 15px 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.error-card p {
    color: #721c24;
    margin: 0 0 15px 0;
    font-size: 1rem;
    line-height: 1.6;
}

.error-suggestions {
    text-align: left;
    margin: 20px 0;
    padding: 20px;
    background: rgba(220, 53, 69, 0.05);
    border-radius: 8px;
    border-left: 4px solid #dc3545;
}

.error-suggestions h5 {
    color: #721c24;
    margin: 0 0 12px 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.error-suggestions ul {
    margin: 0;
    padding: 0 0 0 20px;
    color: #721c24;
}

.error-suggestions li {
    margin-bottom: 8px;
    font-size: 0.95rem;
    line-height: 1.4;
}

.error-actions {
    display: grid;
    gap: 12px;
    margin-top: 25px;
}

/* Investment Available Container */
.investment-available-container {
    text-align: center;
    padding: 20px;
}

.balance-status-card {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    max-width: 500px;
    margin: 0 auto 20px auto;
}

.status-icon {
    flex-shrink: 0;
}

.status-content {
    text-align: left;
}

.status-content h4 {
    color: #155724;
    margin: 0 0 8px 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.status-content p {
    color: #155724;
    margin: 0;
    font-size: 0.95rem;
    line-height: 1.4;
}

.investment-actions {
    display: grid;
    gap: 12px;
    max-width: 400px;
    margin: 0 auto;
}

/* Button enhancements */
.btn svg {
    margin-right: 8px;
    vertical-align: middle;
}

/* ========================================
   INVESTMENT HISTORY TABLE RESPONSIVE STYLES
   ======================================== */

/* Investment History Table - Mobile First Design */
.table-responsive {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border-radius: 8px;
    background: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    border: 1px solid #e9ecef;
    opacity: 0;
    transform: translateY(10px);
    animation: tableSlideIn 0.4s ease-out forwards;
}

@keyframes tableSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mobile scroll hint for better UX */
@media (max-width: 768px) {
    .table-responsive::after {
        content: '← Scroll to see more →';
        position: absolute;
        bottom: -25px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 0.7rem;
        color: #6c757d;
        background: rgba(255, 255, 255, 0.9);
        padding: 4px 8px;
        border-radius: 4px;
        white-space: nowrap;
        z-index: 1;
        animation: fadeInHint 0.5s ease-out 1s forwards;
        opacity: 0;
    }
    
    .table-responsive.scrolled-left::after,
    .table-responsive.scrolled-right::after {
        display: none;
    }
}

@keyframes fadeInHint {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(5px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* Hide hint after user starts scrolling */
.table-responsive.user-scrolled::after {
    animation: fadeOutHint 0.3s ease-out forwards;
}

@keyframes fadeOutHint {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

/* ========================================
   GLOBAL HORIZONTAL SCROLL PREVENTION
   ======================================== */

/* Prevent any element from causing horizontal scroll on mobile */
@media (max-width: 768px) {
    /* Global overflow prevention */
    html {
        overflow-x: hidden;
    }
    
    body {
        overflow-x: hidden;
        position: relative;
    }
    
    /* Ensure main container respects viewport bounds */
    .app-container {
        overflow-x: hidden;
        max-width: 100vw;
        box-sizing: border-box;
    }
    
    /* Prevent any card content from overflowing */
    .card {
        overflow-x: hidden;
        max-width: 100%;
        box-sizing: border-box;
    }
    
    .card * {
        max-width: 100%;
        box-sizing: border-box;
    }
    
    /* Specifically target potential overflow sources */
    .investment-history-table {
        /* Ensure table doesn't break out of container */
        box-sizing: border-box;
    }
    
    /* Fix footer menu positioning issues */
    .footer-menu {
        /* Ensure footer doesn't cause horizontal scroll */
        max-width: 100vw;
        box-sizing: border-box;
        left: 0;
        right: 0;
        width: 100%;
    }
    
    .footer-nav {
        /* Prevent footer nav from overflowing */
        max-width: 100%;
        box-sizing: border-box;
        overflow: hidden;
    }
    
    /* Fix any button or element that might overflow */
    .btn,
    button,
    input,
    select,
    textarea {
        max-width: 100%;
        box-sizing: border-box;
    }
    
    /* Ensure consistent padding that doesn't cause overflow */
    .dashboard-content {
        padding-left: clamp(8px, 3vw, 16px);
        padding-right: clamp(8px, 3vw, 16px);
        max-width: 100vw;
        box-sizing: border-box;
    }
}

/* ========================================
   ENHANCED FOOTER MENU SPACING
   ======================================== */

/* Additional spacing adjustments for investment history */
@media (max-width: 768px) {
    /* Ensure adequate space above footer menu */
    .card:last-child,
    .card:has(#investmentHistoryList),
    .investment-history-card {
        margin-bottom: 40px !important;
        padding-bottom: 20px;
    }
    
    /* Add extra bottom padding to main content area */
    .dashboard-content,
    .container {
        padding-bottom: 100px; /* Extra padding for footer menu */
    }
    
    /* Ensure footer menu doesn't cover table scroll area */
    .table-responsive {
        margin-bottom: 20px;
    }
}

/* ========================================
   IMPROVED TABLE RESPONSIVENESS
   ======================================== */

/* Better table cell content handling on small screens */
@media (max-width: 480px) {
    .investment-history-table {
        /* Further reduce minimum width for very small screens */
        min-width: 520px;
    }
    
    /* Compress table cells more aggressively */
    .investment-history-table th,
    .investment-history-table td {
        padding: 6px 4px;
        font-size: 0.75rem;
    }
    
    /* Make status badges smaller */
    .status-badge {
        padding: 2px 5px;
        font-size: 0.6rem;
    }
    
    /* Compress progress bars */
    .progress-bar {
        min-width: 40px;
        height: 4px;
    }
    
    .progress-text {
        font-size: 0.6rem;
    }
    
    /* Reduce plan cell content */
    .plan-name {
        font-size: 0.75rem;
    }
    
    .plan-type {
        font-size: 0.65rem;
    }
    
    /* Make amount displays more compact */
    .amount-cell .amount,
    .return-amount {
        font-size: 0.75rem;
    }
    
    .amount-cell .currency {
        font-size: 0.65rem;
    }
    
    /* Compact date display */
    .date-main {
        font-size: 0.7rem;
    }

    .date-time {
        font-size: 0.6rem;
    }
}

/* ========================================
   COMPACT DESIGN STYLES
   ======================================== */

/* Compact Make Investment Page */
.make-investment-page.compact {
    padding: 12px;
    max-width: 100%;
}

/* Compact Header with Balance */
.compact-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 16px 20px;
    margin-bottom: 16px;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.compact-header .header-info h2 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.compact-header .header-info p {
    margin: 0;
    font-size: 0.85rem;
    opacity: 0.9;
}

.compact-header .balance-display {
    text-align: right;
    min-width: 120px;
}

.compact-header .balance-label {
    font-size: 0.75rem;
    opacity: 0.8;
    display: block;
}

.compact-header .balance-amount {
    font-size: 1.5rem;
    font-weight: 700;
    display: block;
    line-height: 1.2;
}

.compact-header .balance-currency {
    font-size: 0.8rem;
    opacity: 0.9;
}

/* Compact Investment Plans Section */
.investment-plans-section.compact {
    margin-bottom: 16px;
}

.investment-plans-section.compact .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

.investment-plans-section.compact .section-header h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

.investment-plans-section.compact .plan-count {
    font-size: 0.8rem;
    color: #6c757d;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 12px;
}

/* Compact Investment Plans Grid */
.investment-plans.compact-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 12px;
}

.investment-plans.compact-grid .plan-card {
    padding: 16px;
    border-radius: 10px;
    background: white;
    border: 2px solid #e9ecef;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.investment-plans.compact-grid .plan-card:hover {
    border-color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.investment-plans.compact-grid .plan-card.active {
    border-color: #667eea;
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.investment-plans.compact-grid .plan-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.investment-plans.compact-grid .plan-name {
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
}

.investment-plans.compact-grid .plan-rate {
    font-size: 1.1rem;
    font-weight: 700;
    color: #667eea;
}

.investment-plans.compact-grid .plan-period {
    font-size: 0.75rem;
    color: #6c757d;
    margin-left: 4px;
}

.investment-plans.compact-grid .plan-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
    color: #6c757d;
}

/* Compact Card */
.compact-card {
    background: white;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.compact-card .form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e9ecef;
}

.compact-card .form-header h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

.compact-card .form-balance {
    text-align: right;
    font-size: 0.9rem;
}

.compact-card .form-balance .balance-label {
    color: #6c757d;
    margin-right: 4px;
}

.compact-card .balance-status {
    font-size: 0.75rem;
    margin-top: 2px;
}

.compact-card .balance-status.sufficient {
    color: #28a745;
}

.compact-card .balance-status.insufficient {
    color: #dc3545;
}

/* Compact Plan Summary */
.selected-plan-summary.compact {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
    border: 1px solid #667eea;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 16px;
}

.selected-plan-summary.compact .plan-summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.selected-plan-summary.compact .plan-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.selected-plan-summary.compact .plan-name {
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
}

.selected-plan-summary.compact .plan-rate {
    font-size: 0.85rem;
    color: #667eea;
    font-weight: 500;
}

.selected-plan-summary.compact .plan-details {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 2px;
    font-size: 0.8rem;
    color: #6c757d;
}

/* Compact Form Styles */
.investment-form.compact {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.form-row.compact {
    display: flex;
    gap: 12px;
    align-items: flex-end;
}

.form-row.compact .form-group {
    flex: 1;
}

.form-row.compact .form-group.amount-input {
    flex: 2;
}

.form-row.compact .form-group.calculate-group {
    flex: 1;
}

.form-row.compact .form-group label {
    font-size: 0.9rem;
    font-weight: 500;
    color: #495057;
    margin-bottom: 6px;
    display: block;
}

.form-row.compact .form-group input {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.95rem;
    transition: border-color 0.2s ease;
}

.form-row.compact .form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-row.compact .input-help {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 4px;
}

.form-row.compact .amount-validation {
    font-size: 0.75rem;
    margin-top: 4px;
}

.form-row.compact .amount-validation.valid {
    color: #28a745;
}

.form-row.compact .amount-validation.invalid {
    color: #dc3545;
}

/* Compact Buttons */
.btn.compact {
    padding: 10px 16px;
    font-size: 0.9rem;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.2s ease;
}

.btn.btn-outline.compact {
    background: white;
    border: 2px solid #667eea;
    color: #667eea;
}

.btn.btn-outline.compact:hover {
    background: #667eea;
    color: white;
}

.btn.btn-primary.compact {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
}

.btn.btn-primary.compact:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn.btn-secondary.compact {
    background: #6c757d;
    border: none;
    color: white;
}

.btn.btn-secondary.compact:hover {
    background: #5a6268;
}

/* Compact Investment Summary */
.investment-summary.compact {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    margin-top: 16px;
}

.investment-summary.compact .summary-header {
    margin-bottom: 12px;
}

.investment-summary.compact .summary-header h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
}

.investment-summary.compact .summary-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
}

.investment-summary.compact .summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 8px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.investment-summary.compact .summary-label {
    font-size: 0.75rem;
    color: #6c757d;
    margin-bottom: 2px;
}

.investment-summary.compact .summary-value {
    font-size: 0.9rem;
    font-weight: 600;
    color: #2c3e50;
}

/* Compact Form Actions */
.form-actions.compact {
    display: flex;
    gap: 12px;
    margin-top: 16px;
}

.form-actions.compact .btn {
    flex: 1;
}

/* Compact Warning Card */
.insufficient-balance.compact {
    margin-top: 16px;
}

.warning-card.compact {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 16px;
}

.warning-card.compact .warning-content {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 12px;
}

.warning-card.compact .warning-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.warning-card.compact .warning-text h4 {
    margin: 0 0 4px 0;
    font-size: 1rem;
    color: #856404;
}

.warning-card.compact .warning-text p {
    margin: 0;
    font-size: 0.85rem;
    color: #856404;
}

.warning-actions.compact {
    display: flex;
    gap: 8px;
}

.warning-actions.compact .btn {
    flex: 1;
    padding: 8px 12px;
    font-size: 0.85rem;
}

/* Compact Modal Styles */
.modal.compact {
    padding: 16px;
}

.modal-content.compact {
    max-width: 400px;
    padding: 16px;
    border-radius: 12px;
}

.modal-header.compact {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e9ecef;
}

.modal-header.compact .modal-title {
    display: flex;
    align-items: center;
    gap: 8px;
}

.modal-header.compact .modal-icon,
.modal-header.compact .success-icon {
    font-size: 1.2rem;
}

.modal-header.compact h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6c757d;
    padding: 4px;
    line-height: 1;
}

.modal-close:hover {
    color: #495057;
}

.modal-body.compact {
    margin-bottom: 16px;
}

/* Compact Balance Comparison */
.balance-comparison.compact {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.balance-comparison.compact .comparison-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 3px solid #e9ecef;
}

.balance-comparison.compact .comparison-item.deficit {
    background: #fff5f5;
    border-left-color: #dc3545;
}

.balance-comparison.compact .comparison-label {
    font-size: 0.85rem;
    color: #6c757d;
    font-weight: 500;
}

.balance-comparison.compact .comparison-value {
    font-size: 0.9rem;
    font-weight: 600;
}

.balance-comparison.compact .comparison-value.required {
    color: #495057;
}

.balance-comparison.compact .comparison-value.available {
    color: #28a745;
}

.balance-comparison.compact .comparison-value.deficit {
    color: #dc3545;
}

/* Compact Success Modal */
.success-modal.compact .modal-header.success-header.compact {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    margin: -16px -16px 16px -16px;
    padding: 16px;
    border-radius: 12px 12px 0 0;
    border-bottom: none;
}

.success-modal.compact .success-title {
    display: flex;
    align-items: center;
    gap: 8px;
}

.success-modal.compact .success-title .success-icon {
    font-size: 1.2rem;
}

.success-modal.compact .success-title h3 {
    color: white;
}

.success-modal.compact .modal-close {
    color: white;
}

.success-modal.compact .modal-close:hover {
    color: rgba(255, 255, 255, 0.8);
}

/* Compact Success Details */
.success-details .detail-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
}

.success-details .detail-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.success-details .detail-label {
    font-size: 0.75rem;
    color: #6c757d;
    margin-bottom: 2px;
}

.success-details .detail-value {
    font-size: 0.9rem;
    font-weight: 600;
    color: #2c3e50;
}

/* Compact Modal Footer */
.modal-footer.compact {
    display: flex;
    gap: 8px;
    margin-top: 16px;
}

.modal-footer.compact .btn {
    flex: 1;
    padding: 10px 12px;
    font-size: 0.85rem;
}

.modal-footer.compact .btn-icon {
    margin-right: 4px;
}

/* Mobile Responsive Adjustments for Compact Design */
@media (max-width: 768px) {
    .compact-header {
        flex-direction: column;
        gap: 12px;
        text-align: center;
        padding: 16px;
    }

    .compact-header .balance-display {
        text-align: center;
        min-width: auto;
    }

    .form-row.compact {
        flex-direction: column;
        gap: 16px;
    }

    .form-row.compact .form-group.amount-input,
    .form-row.compact .form-group.calculate-group {
        flex: none;
    }

    .investment-summary.compact .summary-grid {
        grid-template-columns: 1fr;
        gap: 6px;
    }

    .success-details .detail-grid {
        grid-template-columns: 1fr;
        gap: 6px;
    }

    .modal-footer.compact {
        flex-direction: column;
    }

    .warning-actions.compact {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .make-investment-page.compact {
        padding: 8px;
    }

    .compact-header {
        padding: 12px;
        margin-bottom: 12px;
    }

    .compact-card {
        padding: 12px;
        margin-bottom: 12px;
    }

    .modal-content.compact {
        margin: 20px 8px;
        max-width: none;
        width: calc(100% - 16px);
    }
}
