<?php
// Include frontend autoloader
require_once __DIR__ . '/autoload.php';

use Frontend\Config\FrontendConfig;
use Frontend\Services\SessionService;

// Initialize configuration and session
FrontendConfig::init();
SessionService::init();

// Check if user is logged in
$isLoggedIn = SessionService::isAuthenticated();
$isAdmin = SessionService::isAdmin();

// Redirect to admin if admin user
if ($isLoggedIn && $isAdmin && !isset($_GET['user_view'])) {
    header('Location: admin.php');
    exit;
}

// Redirect to dashboard if logged in
if ($isLoggedIn && !isset($_GET['logout'])) {
    header('Location: user/dashboard.php');
    exit;
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: index.php');
    exit;
}

// Redirect to new modern login page
header('Location: login.php');
exit;

