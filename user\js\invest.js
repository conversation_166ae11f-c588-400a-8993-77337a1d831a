// Investment Page JavaScript - SECURITY HARDENED VERSION
let securityUtils = null;
let csrfManager = null;

// Load security utilities first
async function loadSecurityUtils() {
    if (window.SecurityUtils) {
        securityUtils = window.SecurityUtils;
        return;
    }
    
    try {
        await new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'js/security-utils.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
        securityUtils = window.SecurityUtils;
    } catch (error) {
        console.error('Failed to load security utilities:', error);
        // Fallback security function
        securityUtils = {
            escapeHtml: (str) => String(str).replace(/[&<>"']/g, (match) => ({
                '&': '&amp;', '<': '&lt;', '>': '&gt;', '"': '&quot;', "'": '&#39;'
            })[match])
        };
    }
}

document.addEventListener('DOMContentLoaded', async function() {
    await loadSecurityUtils();
    
    // Initialize CSRF manager
    if (typeof CSRFManager !== 'undefined') {
        csrfManager = new CSRFManager();
        csrfManager.setContext('user');
    }
    
    initInvestPage();
});

let userBalance = 0;
let selectedPlan = 'basic';
let investmentPlans = {}; // Will be loaded from API
let systemConfig = null; // Will store system configuration

function initInvestPage() {
    loadSystemConfiguration();
    loadInvestmentPlans();
    loadUserBalance();
    loadActiveInvestments();
    loadInvestmentHistory();
    initFooterMenu();
}

function setupInvestmentForm() {
    const investForm = document.getElementById('investmentForm');
    const calculateBtn = document.getElementById('calculateBtn');
    const investAmountInput = document.getElementById('investAmount');
    const planCards = document.querySelectorAll('.plan-card');

    // Only setup plan selection if plan cards exist
    if (planCards.length > 0) {
        // Plan selection
        planCards.forEach(card => {
            card.addEventListener('click', function() {
                // Remove active class from all cards
                planCards.forEach(c => c.classList.remove('active'));
                // Add active class to clicked card
                this.classList.add('active');
                
                selectedPlan = this.dataset.plan;
                const selectedPlanInput = document.getElementById('selectedPlan');
                if (selectedPlanInput) {
                    selectedPlanInput.value = selectedPlan;
                }
                  // Update minimum amount
                const minAmount = investmentPlans[selectedPlan].minAmount;
                if (investAmountInput) {
                    investAmountInput.min = minAmount;
                    investAmountInput.placeholder = `Enter amount (minimum ${securityUtils.escapeHtml(minAmount)} USDT)`;
                }
                
                // Clear previous calculations
                const investmentSummary = document.getElementById('investmentSummary');
                const investBtn = document.getElementById('investBtn');
                if (investmentSummary) investmentSummary.style.display = 'none';
                if (investBtn) investBtn.disabled = true;
            });
        });

        // Set default selected plan (only if basic plan card exists)
        const basicPlanCard = document.querySelector('[data-plan="basic"]');
        if (basicPlanCard) {
            basicPlanCard.classList.add('active');
        }
    }

    // Calculate button
    if (calculateBtn) {
        calculateBtn.addEventListener('click', calculateReturns);
    }

    // Form submission
    if (investForm) {
        investForm.addEventListener('submit', handleInvestment);
    }

    // Amount input validation
    if (investAmountInput) {
        investAmountInput.addEventListener('input', function() {
            const investmentSummary = document.getElementById('investmentSummary');
            const investBtn = document.getElementById('investBtn');
            if (investmentSummary) investmentSummary.style.display = 'none';
            if (investBtn) investBtn.disabled = true;
        });
    }
}

async function loadUserBalance() {
    try {
        const response = await apiCall('get_balance');
        
        // Handle successful response or when balance is available
        if (response.success || (response.balance !== undefined && !response.error)) {
            userBalance = parseFloat(response.balance) || 0;
            const balanceElement = document.getElementById('availableBalance');
            if (balanceElement) {
                balanceElement.textContent = userBalance.toFixed(2);
            }
            
            // Update balance-based container content
            updateBalanceBasedContent();
        } else if (response.error === "Main wallet not found") {
            // This is expected for new users who don't have a wallet yet
            // Treat as zero balance
            userBalance = 0;
            const balanceElement = document.getElementById('availableBalance');
            if (balanceElement) {
                balanceElement.textContent = '0.00';
            }
            
            // Update balance-based container content for new user
            updateBalanceBasedContent();
        } else {
            console.error('Error loading balance:', response.message || response.error);
            const balanceElement = document.getElementById('availableBalance');
            if (balanceElement) balanceElement.textContent = 'Error';
            
            // Show error message
            showBalanceError();
        }
    } catch (error) {
        console.error('Error loading balance:', error);
        const balanceElement = document.getElementById('availableBalance');
        if (balanceElement) balanceElement.textContent = 'Error';
        
        // Show error message
        showBalanceError();
    }
}

// Update balance-based container content
function updateBalanceBasedContent() {
    const balanceBasedContainer = document.getElementById('balanceBasedContainer');
    const insufficientMsg = document.getElementById('insufficientBalanceMsg');
    const investForm = document.getElementById('investmentForm');
    
    if (!balanceBasedContainer) return;
    
    if (userBalance < 300) {
        // Determine if user is completely new (0 balance) or just has insufficient funds
        const isNewUser = userBalance === 0;
        const title = isNewUser ? "Welcome! Ready to Start Investing?" : "Insufficient Balance";
        const subtitle = isNewUser ? 
            "You're just one step away from earning daily returns on your USDT!" :
            `Your current balance is <strong>${securityUtils.escapeHtml(userBalance.toFixed(2))} USDT</strong>`;
        
        // Show insufficient balance message with appropriate messaging
        balanceBasedContainer.innerHTML = `
            <div class="insufficient-balance-container">
                <div class="warning-card">
                    <div class="warning-icon">
                        ${isNewUser ? `
                            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#28a745" stroke-width="2">
                                <circle cx="12" cy="12" r="10"></circle>
                                <path d="M9,12l2,2 4,-4"></path>
                            </svg>
                        ` : `
                            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#ffc107" stroke-width="2">
                                <circle cx="12" cy="12" r="10"></circle>
                                <line x1="12" y1="8" x2="12" y2="12"></line>
                                <line x1="12" y1="16" x2="12.01" y2="16"></line>
                            </svg>
                        `}
                    </div>
                    <h4>${title}</h4>
                    <p>${subtitle}</p>
                    <p>You need at least <strong>300 USDT</strong> to start investing and earn daily returns.</p>
                    ${!isNewUser ? `
                        <div class="balance-needed">
                            <span class="needed-amount">Amount needed: <strong>${securityUtils.escapeHtml((300 - userBalance).toFixed(2))} USDT</strong></span>
                        </div>
                    ` : ''}
                    <div class="investment-benefits">
                        <h5>Why Invest with Us?</h5>
                        <ul>
                            <li>Daily returns on your investment</li>
                            <li>Secure USDT-based transactions</li>
                            <li>Transparent investment tracking</li>
                            <li>Flexible investment plans</li>
                        </ul>
                    </div>
                    <div class="warning-actions">
                        <a href="deposit.php" class="btn btn-primary">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="12" y1="5" x2="12" y2="19"></line>
                                <line x1="5" y1="12" x2="19" y2="12"></line>
                            </svg>
                            ${isNewUser ? 'Make Your First Deposit' : 'Add Funds'}
                        </a>
                        <a href="dashboard.php" class="btn btn-secondary">Back to Dashboard</a>
                    </div>
                </div>
            </div>
        `;
        
        // Hide the separate insufficient message and form
        if (insufficientMsg) insufficientMsg.style.display = 'none';
        if (investForm) investForm.style.display = 'none';
    } else {
        // Show investment form
        balanceBasedContainer.innerHTML = `
            <div class="investment-available-container">
                <div class="balance-status-card">
                    <div class="status-icon">
                        <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="#28a745" stroke-width="2">
                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                            <polyline points="22,4 12,14.01 9,11.01"></polyline>
                        </svg>
                    </div>
                    <div class="status-content">
                        <h4>Ready to Invest</h4>
                        <p>Your balance: <strong>${securityUtils.escapeHtml(userBalance.toFixed(2))} USDT</strong></p>
                        <p>You can start investing now!</p>
                    </div>
                </div>
                <div class="investment-actions">
                    <a href="make_investment.php" class="btn btn-primary">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="12" y1="5" x2="12" y2="19"></line>
                            <line x1="5" y1="12" x2="19" y2="12"></line>
                        </svg>
                        Start Investment
                    </a>
                    <a href="deposit.php" class="btn btn-outline">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="12" y1="5" x2="12" y2="19"></line>
                            <line x1="5" y1="12" x2="19" y2="12"></line>
                        </svg>
                        Add More Funds
                    </a>
                </div>
            </div>
        `;
        
        // Hide the separate insufficient message and show form
        if (insufficientMsg) insufficientMsg.style.display = 'none';
        if (investForm) investForm.style.display = 'block';
    }
}

function calculateReturns() {
    const amountInput = document.getElementById('investAmount');
    if (!amountInput) return;
      const amount = parseFloat(amountInput.value);
    
    if (!amount || amount < investmentPlans[selectedPlan].minAmount) {
        showMessage(`Minimum investment for ${securityUtils.escapeHtml(investmentPlans[selectedPlan].name)} is ${securityUtils.escapeHtml(investmentPlans[selectedPlan].minAmount)} USDT`, 'error');
        return;
    }
    
    if (amount > userBalance) {
        showMessage('Insufficient balance for this investment amount', 'error');
        return;
    }
    
    const plan = investmentPlans[selectedPlan];
    const dailyReturn = amount * plan.dailyRate;
    const totalReturn = dailyReturn * plan.duration;
      // Update summary
    const summaryAmount = document.getElementById('summaryAmount');
    const summaryDailyReturn = document.getElementById('summaryDailyReturn');
    const summaryTotalReturn = document.getElementById('summaryTotalReturn');
    
    if (summaryAmount) summaryAmount.textContent = `${amount.toFixed(2)} USDT`;
    if (summaryDailyReturn) summaryDailyReturn.textContent = `${dailyReturn.toFixed(2)} USDT`;
    if (summaryTotalReturn) summaryTotalReturn.textContent = `${totalReturn.toFixed(2)} USDT`;
    
    const investmentSummary = document.getElementById('investmentSummary');
    const investBtn = document.getElementById('investBtn');
    if (investmentSummary) investmentSummary.style.display = 'block';
    if (investBtn) investBtn.disabled = false;
}

async function handleInvestment(e) {
    e.preventDefault();
    
    const investAmountInput = document.getElementById('investAmount');
    if (!investAmountInput) return;
      const amount = parseFloat(investAmountInput.value);
    
    if (!amount || amount < investmentPlans[selectedPlan].minAmount) {
        showMessage(`Minimum investment for ${securityUtils.escapeHtml(investmentPlans[selectedPlan].name)} is ${securityUtils.escapeHtml(investmentPlans[selectedPlan].minAmount)} USDT`, 'error');
        return;
    }
    
    if (amount > userBalance) {
        showMessage('Insufficient balance for this investment amount', 'error');
        return;
    }
    
    // Disable button to prevent double submission
    const investBtn = document.getElementById('investBtn');
    if (!investBtn) return;
    
    const originalText = investBtn.textContent;
    investBtn.disabled = true;
    investBtn.textContent = 'Processing...';
    
    try {
        const response = await apiCall('create_investment', {
            amount: amount,
            plan: selectedPlan
        });
          if (response.success) {
            showMessage('Investment created successfully!', 'success');
            
            // Reset form
            const investmentForm = document.getElementById('investmentForm');
            const investmentSummary = document.getElementById('investmentSummary');
            const basicPlanCard = document.querySelector('[data-plan="basic"]');
            const otherPlanCards = document.querySelectorAll('.plan-card:not([data-plan="basic"])');
            
            if (investmentForm) investmentForm.reset();
            if (investmentSummary) investmentSummary.style.display = 'none';
            if (basicPlanCard) basicPlanCard.classList.add('active');
            if (otherPlanCards.length > 0) {
                otherPlanCards.forEach(card => {
                    card.classList.remove('active');
                });
            }
            
            // Reload data
            loadUserBalance();
            loadActiveInvestments();
            loadInvestmentHistory();
        } else {
            showMessage(response.message || 'Failed to create investment', 'error');
        }
    } catch (error) {
        console.error('Error creating investment:', error);
        showMessage('Network error. Please try again.', 'error');
    } finally {
        investBtn.disabled = false;
        investBtn.textContent = originalText;
    }
}

async function loadActiveInvestments() {
    try {
        const response = await apiCall('get_active_investments');
        const container = document.getElementById('activeInvestmentsList');
        
        if (!container) return;          if (response.success && response.investments && response.investments.length > 0) {            container.innerHTML = response.investments.map(investment => `
                <div class="investment-item modern-layout" data-investment-id="${securityUtils.escapeHtml(investment.id)}" style="cursor: pointer;">
                    <div class="investment-header-new">
                        <span class="plan-name">${securityUtils.escapeHtml(investment.plan_name)}</span>
                        <span class="investment-value">${securityUtils.escapeHtml(parseFloat(investment.amount).toFixed(2))} USDT</span>
                    </div>
                    <div class="investment-progress-new">
                        <div class="progress-bar-new">
                            <div class="progress-fill-new" style="width: ${Math.round((investment.days_elapsed / 20) * 100)}%"></div>
                        </div>
                        <span class="progress-percent">${Math.round((investment.days_elapsed / 20) * 100)}%</span>
                    </div>
                    <div class="investment-metrics">
                        <div class="metric-item">
                            <span class="metric-value">${securityUtils.escapeHtml(investment.days_elapsed)}/20</span>
                            <span class="metric-label">Days</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-value">+${securityUtils.escapeHtml(parseFloat(investment.daily_return).toFixed(2))}</span>
                            <span class="metric-label">USDT/day</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-value">${securityUtils.escapeHtml(parseFloat(investment.total_earned).toFixed(2))}</span>
                            <span class="metric-label">Total Earned</span>
                        </div>
                    </div>
                </div>
            `).join('');
            
            // Add click event listeners to investment items
            container.addEventListener('click', (e) => {
                const investmentItem = e.target.closest('.investment-item');
                if (investmentItem) {
                    const investmentId = investmentItem.dataset.investmentId;
                    if (investmentId) {
                        // Add click animation
                        investmentItem.style.transform = 'scale(0.98)';
                        setTimeout(() => {
                            investmentItem.style.transform = '';
                            // Navigate to active plan page
                            window.location.href = `active_plan.php?id=${securityUtils.escapeHtml(investmentId)}`;
                        }, 150);
                    }
                }
            });
        } else {
            container.innerHTML = '<div class="no-data">No active investments found</div>';        }
    } catch (error) {
        console.error('Error loading active investments:', error);
        if (container) {
            container.innerHTML = '<div class="error">Error loading investments</div>';
        }
    }
}

async function loadInvestmentHistory() {
    try {
        const response = await apiCall('get_investment_history');
        const container = document.getElementById('investmentHistoryList');
        
        if (!container) return;
          if (response.success && response.history && response.history.data && response.history.data.length > 0) {
            container.innerHTML = `
                <div class="table-responsive">
                    <table class="investment-history-table">
                        <thead>
                            <tr>
                                <th>Plan</th>
                                <th>Amount</th>
                                <th>Status</th>
                                <th>Progress</th>
                                <th>Total Return</th>
                                <th>Date Created</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${response.history.data.map(investment => `
                                <tr class="investment-row">
                                    <td class="plan-cell">
                                        <div class="plan-name">${securityUtils.escapeHtml(investment.plan_name)}</div>
                                        <div class="plan-type">${securityUtils.escapeHtml(investment.plan_type)}</div>
                                    </td>
                                    <td class="amount-cell">
                                        <span class="amount">${securityUtils.escapeHtml(parseFloat(investment.amount).toFixed(2))}</span>
                                        <span class="currency">USDT</span>
                                    </td>
                                    <td class="status-cell">
                                        <span class="status-badge ${securityUtils.escapeHtml(investment.status)}">${securityUtils.escapeHtml(investment.status.toUpperCase())}</span>
                                    </td>
                                    <td class="progress-cell">
                                        <div class="progress-container">
                                            <div class="progress-bar">
                                                <div class="progress-fill" style="width: ${Math.min(100, Math.max(0, investment.progress || 0))}%"></div>
                                            </div>
                                            <span class="progress-text">${parseFloat(investment.progress || 0).toFixed(1)}%</span>
                                        </div>
                                    </td>
                                    <td class="return-cell">
                                        <span class="return-amount">${securityUtils.escapeHtml(parseFloat(investment.total_return).toFixed(2))}</span>
                                        <span class="currency">USDT</span>
                                    </td>
                                    <td class="date-cell">
                                        <div class="date-main">${securityUtils.escapeHtml(new Date(investment.created_at).toLocaleDateString())}</div>
                                        <div class="date-time">${securityUtils.escapeHtml(new Date(investment.created_at).toLocaleTimeString([], {hour: '2-digit', minute: '2-digit'}))}</div>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
            
            // Add scroll detection for better UX
            const tableResponsive = container.querySelector('.table-responsive');
            if (tableResponsive) {
                // Add investment-history-card class for styling
                const card = container.closest('.card');
                if (card) {
                    card.classList.add('investment-history-card');
                }
                
                // Handle scroll indicators
                function updateScrollIndicators() {
                    const scrollLeft = tableResponsive.scrollLeft;
                    const scrollWidth = tableResponsive.scrollWidth;
                    const clientWidth = tableResponsive.clientWidth;
                    const maxScroll = scrollWidth - clientWidth;
                    
                    // Remove existing classes
                    tableResponsive.classList.remove('scrolled-left', 'scrolled-right');
                    
                    // Add appropriate classes based on scroll position
                    if (scrollLeft > 5) {
                        tableResponsive.classList.add('scrolled-left');
                    }
                    if (scrollLeft < maxScroll - 5) {
                        tableResponsive.classList.add('scrolled-right');
                    }
                }
                
                // Add scroll event listener
                tableResponsive.addEventListener('scroll', function(e) {
                    updateScrollIndicators();
                    // Hide scroll hint after user starts scrolling
                    e.target.classList.add('user-scrolled');
                });
                
                // Initial check
                setTimeout(updateScrollIndicators, 100);
                
                // Update on window resize
                window.addEventListener('resize', updateScrollIndicators);
                
                // Add mobile touch scrolling enhancement
                if (window.innerWidth <= 768) {
                    tableResponsive.style.webkitOverflowScrolling = 'touch';
                    tableResponsive.setAttribute('tabindex', '0');
                    tableResponsive.setAttribute('aria-label', 'Investment history table - scroll horizontally to see more data');
                }
            }
        } else {
            container.innerHTML = `
                <div class="table-responsive">
                    <div class="no-data-message">
                        <div class="no-data-icon">📊</div>
                        <h4>No Investment History</h4>
                        <p>You haven't made any investments yet. Start investing to see your history here.</p>
                    </div>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading investment history:', error);
        const container = document.getElementById('investmentHistoryList');
        if (container) {
            container.innerHTML = '<div class="error-message">Error loading investment history</div>';
        }
    }
}

async function loadSystemConfiguration() {
    try {
        const response = await apiCall('get_system_configuration');
        if (response.success) {
            systemConfig = response.data;
        } else {
            console.error('Failed to load system configuration:', response.message);
        }
    } catch (error) {
        console.error('Error loading system configuration:', error);
    }
}

async function loadInvestmentPlans() {
    try {
        const response = await apiCall('get_investment_plans');
        
        if (response.success) {
            const plans = response.plans;
            
            // Convert plans array to object for easy access
            investmentPlans = {};
            plans.forEach(plan => {
                investmentPlans[plan.plan_code] = {
                    id: plan.id,
                    name: plan.plan_name,
                    dailyRate: plan.daily_rate,
                    duration: plan.duration,
                    minAmount: plan.min_amount,
                    maxAmount: plan.max_amount,
                    description: plan.description,
                    features: plan.features || [],
                    isActive: plan.is_active,
                    isFeatured: plan.is_featured
                };
            });
            
            setupInvestmentForm();
        } else {
            console.error('Failed to load investment plans:', response.message);
            // Fallback to basic plan only
            useDefaultPlans();
        }
    } catch (error) {
        console.error('Error loading investment plans:', error);
        useDefaultPlans();
    }
}

function useDefaultPlans() {
    investmentPlans = {
        basic: {
            name: 'Basic Plan',
            dailyRate: 0.0625,
            duration: 20,
            minAmount: 300
        }
    };
    setupInvestmentForm();
}

// API call function with CSRF protection
async function apiCall(endpoint, data = {}) {
    // Define state-changing operations that require CSRF protection
    const stateChangingActions = [
        'create_investment', 'withdraw', 'record_deposit', 'change_password'
    ];
    
    // Use CSRF manager for state-changing operations
    if (csrfManager && stateChangingActions.includes(endpoint)) {
        try {
            return await csrfManager.secureRequest(endpoint, data);
        } catch (error) {
            console.error('CSRF secure request failed:', error);
            // Fallback to regular request but show error
            showMessage('Security token error. Please refresh the page and try again.', 'error');
            throw error;
        }
    }
    
    // Regular API call for read-only operations
    try {
        const response = await fetch('../ajax.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: endpoint,
                ...data
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error('API call failed:', error);
        throw error;
    }
}

// Message display function
function showMessage(message, type = 'info') {
    const messageDiv = document.getElementById('message');
    if (messageDiv) {
        messageDiv.textContent = message;
        messageDiv.className = `message ${type}`;
        messageDiv.style.display = 'block';
        
        setTimeout(() => {
            messageDiv.style.display = 'none';
        }, 5000);
    }
}

// Footer menu initialization
function initFooterMenu() {
    // Footer menu is already initialized by the main layout
}

function showBalanceError() {
    const balanceBasedContainer = document.getElementById('balanceBasedContainer');
    
    if (!balanceBasedContainer) return;
    
    balanceBasedContainer.innerHTML = `
        <div class="error-container">
            <div class="error-card">
                <div class="error-icon">
                    <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#dc3545" stroke-width="2">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="15" y1="9" x2="9" y2="15"></line>
                        <line x1="9" y1="9" x2="15" y2="15"></line>
                    </svg>
                </div>
                <h4>Unable to Load Balance</h4>
                <p>We couldn't load your account balance. This might be a temporary connection issue.</p>
                <div class="error-suggestions">
                    <h5>What you can do:</h5>
                    <ul>
                        <li>Check your internet connection</li>
                        <li>Try refreshing the page</li>
                        <li>If the problem persists, contact support</li>
                    </ul>
                </div>
                <div class="error-actions">
                    <button onclick="window.location.reload()" class="btn btn-primary">Refresh Page</button>
                    <a href="dashboard.php" class="btn btn-secondary">Back to Dashboard</a>
                    <a href="deposit.php" class="btn btn-outline">Continue to Deposit</a>
                </div>
            </div>
        </div>
    `;
}
