// Dashboard JavaScript - Simplified for Dashboard Only
// SECURITY FIX: Load security utilities first
document.addEventListener('DOMContentLoaded', function() {
    // Ensure security utilities are loaded
    if (typeof SecurityUtils === 'undefined') {
        const script = document.createElement('script');
        script.src = 'js/security-utils.js';
        script.onload = function() {
            initDashboard();
        };
        document.head.appendChild(script);
    } else {
        initDashboard();
    }
});

function initDashboard() {
    loadDashboardData();
    loadActiveInvestments();
    initInvestmentInteractions();
    initFooterMenu();
}

async function loadDashboardData() {
    try {        // Load balance for dashboard
        const balanceResponse = await apiCall('get_balance');
        const totalBalanceElement = document.getElementById('totalBalance');
        
        if (totalBalanceElement) {
            if (balanceResponse.success) {
                totalBalanceElement.textContent = balanceResponse.balance_formatted || '0.000000';
            } else {
                totalBalanceElement.textContent = '0.000000';
            }
        }
          // Load transaction statistics
        const statsResponse = await apiCall('get_transaction_statistics');
        if (statsResponse.success) {
            const stats = statsResponse.statistics;
            const totalTransactionsElement = document.getElementById('totalTransactions');
            const totalDepositsElement = document.getElementById('totalDeposits');
            
            if (totalTransactionsElement) {
                totalTransactionsElement.textContent = stats.total_transactions || '0';
            }
            if (totalDepositsElement) {
                totalDepositsElement.textContent = stats.total_deposits || '0';
            }
        }
        
        // Load recent transactions for dashboard
        const transactionsResponse = await apiCall('get_transactions', { limit: 5, page: 0 });
        if (transactionsResponse.success) {
            displayRecentTransactions(transactionsResponse.transactions);
        }

        // Load investment statistics
        await loadInvestmentStatistics();
        
    } catch (error) {
        console.error('Error loading dashboard data:', error);
        showMessage('Error loading dashboard data', 'error');
    }
}

// Load investment statistics for dashboard
async function loadInvestmentStatistics() {
    try {
        const response = await apiCall('get_investment_statistics');
        if (response.success) {
            const stats = response.statistics;
            // Update total invested in dashboard stats
            const totalInvestedElement = document.getElementById('totalInvested');
            if (totalInvestedElement) {
                totalInvestedElement.textContent = `${parseFloat(stats.total_invested || 0).toFixed(0)} USDT`;
            }
        }
    } catch (error) {
        console.error('Error loading investment statistics:', error);
    }
}

function displayRecentTransactions(transactions) {
    const container = document.getElementById('recentTransactionsList');
    
    if (!container) {
        return;
    }
    
    if (!transactions || transactions.length === 0) {
        container.innerHTML = '<div class="no-data">No recent transactions</div>';
        return;
    }
    // Helper function to determine if transaction is negative (deduction)
    function isNegativeTransaction(type) {
        const negativeTypes = ['withdrawal', 'investment', 'deduction', 'fee', 'sweep'];
        return negativeTypes.includes(type.toLowerCase());
    }
    
    const transactionsHtml = transactions.map(tx => {
        const isNegative = isNegativeTransaction(tx.type);
        return `
        <div class="transaction-item">
            <div class="transaction-info">
                <div class="transaction-type">${formatTransactionType(tx.type)}</div>
                <div class="transaction-date">${formatDate(tx.created_at)}</div>
            </div>
            <div class="transaction-amount ${isNegative ? 'negative' : 'positive'}">
                ${isNegative ? '-' : '+'}${tx.amount_formatted || tx.amount} TRX
            </div>
        </div>
    `;
    }).join('');
    
    container.innerHTML = transactionsHtml;
}

function formatTransactionType(type) {
    const types = {
        'deposit': 'Deposit',
        'withdrawal': 'Withdrawal',
        'investment': 'Investment',
        'deduction': 'Deduction',
        'fee': 'Fee',
        'transfer': 'Transfer',
        'sweep': 'Sweep',
        'earnings': 'Earnings',
        'bonus': 'Bonus',
        'referral': 'Referral Bonus'
    };
    return types[type] || type.charAt(0).toUpperCase() + type.slice(1);
}

function formatDate(dateString) {
    if (!dateString) return 'N/A';
    
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
}

// Shared utility functions
async function apiCall(endpoint, data = null) {
    try {
        // SECURITY FIX: Use CSRF protection for state-changing operations
        if (window.csrfManager) {
            return await window.csrfManager.stateChangingRequest(endpoint, data || {});
        } else {
            // Fallback for when CSRF manager is not available
            const url = `../ajax.php`;
            const options = {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: endpoint,
                    ...data
                })
            };
            
            const response = await fetch(url, options);
            return await response.json();
        }
    } catch (error) {
        console.error('API call error:', error);
        throw error;
    }
}

function showMessage(message, type = 'info') {
    const messageDiv = document.getElementById('message');
    if (messageDiv) {
        messageDiv.textContent = message;
        messageDiv.className = `message ${type}`;
        messageDiv.style.display = 'block';
        
        setTimeout(() => {
            messageDiv.style.display = 'none';
        }, 3000);
    } else {
        // Fallback to console if message element doesn't exist
    }
}

function initFooterMenu() {
    // Add smooth entrance animation for footer menu
    const footerMenu = document.querySelector('.footer-menu');
    if (footerMenu) {
        // Always show footer menu
        footerMenu.classList.add('show');
        
        // Optional: Handle window resize for future mobile-specific behavior
        window.addEventListener('resize', function() {
            // Footer is always visible now, but this can be customized later
            footerMenu.classList.add('show');
        });
    }
}

// Enhanced Active Investments functionality for Dashboard
async function loadActiveInvestments() {
    try {
        const response = await apiCall('get_active_investments');
        const container = document.getElementById('activeInvestmentsList');
        
        if (!container) return;
        
        // Show enhanced loading state
        container.innerHTML = `
            <div class="loading-investments">
                <div class="loading-spinner-container">
                    <div class="loading-spinner-ring"></div>
                </div>
                <p class="loading-text">Loading your investments<span class="loading-dots"></span></p>
            </div>
        `;
        
        // Simulate slight delay for better UX
        await new Promise(resolve => setTimeout(resolve, 300));
        
        if (response.success && response.investments && response.investments.length > 0) {
            // For dashboard, show a more compact view
            const totalInvested = response.investments.reduce((sum, inv) => sum + parseFloat(inv.amount), 0);
            const totalEarned = response.investments.reduce((sum, inv) => sum + parseFloat(inv.total_earned), 0);
            const dailyEarnings = response.investments.reduce((sum, inv) => sum + parseFloat(inv.daily_return), 0);
            
            container.innerHTML = `
                <div class="investment-summary-card">
                    <h4 style="margin: 0 0 16px 0; font-size: 1.1rem; font-weight: 600;">💼 Portfolio Overview</h4>
                    <div class="summary-stats">
                        <div class="summary-stat">
                            <div class="summary-stat-value">${response.investments.length}</div>
                            <div class="summary-stat-label">Active</div>
                        </div>
                        <div class="summary-stat">
                            <div class="summary-stat-value">${totalInvested.toFixed(2)}</div>
                            <div class="summary-stat-label">Invested</div>
                        </div>
                        <div class="summary-stat">
                            <div class="summary-stat-value">+${dailyEarnings.toFixed(2)}</div>
                            <div class="summary-stat-label">Daily</div>
                        </div>
                        <div class="summary-stat">
                            <div class="summary-stat-value">${totalEarned.toFixed(2)}</div>
                            <div class="summary-stat-label">Total Earned</div>
                        </div>
                    </div>
                </div>
                <div class="investment-grid">                    ${response.investments.slice(0, 2).map((investment, index) => {
                        // SECURITY FIX: Escape all user data to prevent XSS
                        const safePlanName = SecurityUtils.escapeHtml(investment.plan_name);
                        const safeAmount = SecurityUtils.escapeHtml(parseFloat(investment.amount).toFixed(2));
                        const safeDailyReturn = SecurityUtils.escapeHtml(parseFloat(investment.daily_return).toFixed(2));
                        const safeTotalEarned = SecurityUtils.escapeHtml(parseFloat(investment.total_earned).toFixed(2));
                        const safeInvestmentId = SecurityUtils.escapeHtml(investment.id);
                        
                        // Use backend-calculated progress values (now based on actual dates)
                        const progressPercentage = Math.round((investment.days_elapsed / 20) * 100);
                        const isCompleted = investment.days_elapsed >= 20;
                        const statusClass = isCompleted ? 'completed' : 'active';
                        const daysRemaining = investment.days_remaining || Math.max(0, 20 - investment.days_elapsed);
                        
                        return `
                            <div class="investment-item ${index === 0 ? 'pulse' : ''}" data-investment-id="${safeInvestmentId}">
                                <div class="investment-status ${statusClass}">
                                    ${isCompleted ? '✅ Done' : '🔄 Active'}
                                </div>
                                <div class="investment-info">
                                    <div class="investment-details">
                                        <div class="investment-plan">${safePlanName}</div>
                                        <div class="investment-amount">${safeAmount} USDT</div>
                                        <div class="performance-indicator positive">
                                            Performing Well
                                        </div>
                                    </div>
                                    <div class="investment-earnings">
                                        <div class="daily-return">+${safeDailyReturn} USDT/day</div>
                                        <div class="total-earned">${safeTotalEarned} USDT earned</div>
                                    </div>
                                </div>
                                <div class="investment-progress">
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: ${progressPercentage}%"></div>
                                    </div>
                                    <div class="progress-text" data-percentage="${progressPercentage}%">
                                        <span>Day ${investment.days_elapsed}/20</span>
                                        <span style="font-size: 0.8rem; color: #6c757d;">
                                            ${isCompleted ? 'Complete!' : `${daysRemaining} days left`}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        `;
                    }).join('')}
                </div>
                ${response.investments.length > 2 ? `
                    <div style="text-align: center; margin-top: 16px;">
                        <a href="invest.php" class="btn btn-outline" style="padding: 8px 16px; font-size: 0.9rem;">
                            View All ${response.investments.length} Investments
                        </a>
                    </div>
                ` : ''}
            `;
            
            // Add entrance animation
            setTimeout(() => {
                const investmentItems = container.querySelectorAll('.investment-item');
                investmentItems.forEach((item, index) => {
                    setTimeout(() => {
                        item.classList.add('new-investment');
                    }, index * 100);
                });
            }, 100);
            
        } else {
            container.innerHTML = `
                <div class="no-data">
                    <h4>No Active Investments</h4>
                    <p>Start investing today to see your portfolio here!</p>
                </div>
            `;
        }
    } catch (error) {
        console.error('Error loading active investments:', error);
        const container = document.getElementById('activeInvestmentsList');
        if (container) {
            container.innerHTML = `
                <div class="error">
                    <h4>Failed to Load Investments</h4>
                    <p>Please try again later.</p>
                    <button onclick="loadActiveInvestments()" class="btn btn-primary" style="margin-top: 12px;">
                        🔄 Retry
                    </button>
                </div>
            `;
        }
    }
}

// Enhanced investment refresh function with animations
async function refreshActiveInvestments() {
    const container = document.getElementById('activeInvestmentsList');
    if (!container) return;
    
    // Add fade out effect
    container.style.opacity = '0.5';
    container.style.transition = 'opacity 0.3s ease';
    
    try {
        await loadActiveInvestments();
        
        // Add fade in effect
        setTimeout(() => {
            container.style.opacity = '1';
        }, 100);
        
    } catch (error) {
        container.style.opacity = '1';
        console.error('Error refreshing investments:', error);
    }
}

// Investment interaction handlers
function handleInvestmentClick(investmentId) {
    const investmentItem = document.querySelector(`[data-investment-id="${investmentId}"]`);
    if (investmentItem) {
        // Add click animation
        investmentItem.style.transform = 'scale(0.98)';
        setTimeout(() => {
            investmentItem.style.transform = '';
        }, 150);
        
        // Navigate to active plan page
        window.location.href = `active_plan.php?id=${investmentId}`;
    }
}

// Initialize investment interactions
function initInvestmentInteractions() {
    const container = document.getElementById('activeInvestmentsList');
    if (container) {
        container.addEventListener('click', (e) => {
            const investmentItem = e.target.closest('.investment-item');
            if (investmentItem) {
                const investmentId = investmentItem.dataset.investmentId;
                if (investmentId) {
                    handleInvestmentClick(investmentId);
                }
            }
        });
    }
}

// Auto-refresh investments every 5 minutes
function startInvestmentAutoRefresh() {
    setInterval(() => {
        refreshActiveInvestments();
    }, 5 * 60 * 1000); // 5 minutes
}

// Initialize investment interactions and auto-refresh when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        initInvestmentInteractions();
        startInvestmentAutoRefresh();
    }, 1000);
});
