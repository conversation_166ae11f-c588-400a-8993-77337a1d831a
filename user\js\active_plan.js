// Active Plan Page JavaScript - SECURITY HARDENED VERSION
let securityUtils = null;

// Load security utilities first
async function loadSecurityUtils() {
    if (window.SecurityUtils) {
        securityUtils = window.SecurityUtils;
        return;
    }
    
    try {
        await new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'js/security-utils.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
        securityUtils = window.SecurityUtils;
    } catch (error) {
        console.error('Failed to load security utilities:', error);
        // Fallback security function
        securityUtils = {
            escapeHtml: (str) => String(str).replace(/[&<>"']/g, (match) => ({
                '&': '&amp;', '<': '&lt;', '>': '&gt;', '"': '&quot;', "'": '&#39;'
            })[match])
        };
    }
}

document.addEventListener('DOMContentLoaded', async function() {
    await loadSecurityUtils();
    initActivePlanPage();
});

let investmentData = null;
let countdownInterval = null;
let nextPayoutTime = null;
let isLoading = false; // Prevent multiple simultaneous API calls
let isLoadingEarnings = false; // Prevent multiple earnings API calls
let lastLoadTime = 0; // Rate limiting for API calls

function initActivePlanPage() {
    loadInvestmentDetails();
    initFooterMenu();
}

async function loadInvestmentDetails() {
    const now = Date.now();
    if (isLoading || (now - lastLoadTime < 2000)) return; // Prevent calls within 2 seconds
    
    try {
        isLoading = true;
        lastLoadTime = now;
        
        // Load specific investment details
        const response = await apiCall('get_investment_details', { id: INVESTMENT_ID });        if (response.success && response.investment) {
            investmentData = response.investment;

            displayInvestmentDetails();
            loadEarningsHistory();
            initCountdownTimer();
        } else {
            showError('Investment not found or access denied');
        }
    } catch (error) {
        console.error('Error loading investment details:', error);
        showError('Failed to load investment details');
    } finally {
        isLoading = false;
    }
}

function displayInvestmentDetails() {
    if (!investmentData) return;

    // Use backend-calculated values (now properly calculated based on actual dates)
    const currentProgress = investmentData.days_elapsed; // Already calculated as current_date - start_date
    const daysRemaining = investmentData.days_remaining; // Already calculated as 20 - current_progress
    const totalEarned = investmentData.total_earned; // Already calculated as daily_return * current_progress
    
    // Calculate progress percentage based on 20-day duration
    const fixedDuration = 20;
    const progressPercentage = Math.round((currentProgress / fixedDuration) * 100);
    const isCompleted = currentProgress >= fixedDuration;

    // Update plan header
    const planNameElement = document.getElementById('planName');
    const planStatusElement = document.getElementById('planStatus');
    
    if (planNameElement) {
        planNameElement.textContent = investmentData.plan_name || 'Investment Plan';
    }
    
    if (planStatusElement) {
        planStatusElement.textContent = isCompleted ? '✅ Completed' : '🔄 Active';
        planStatusElement.className = `plan-status ${isCompleted ? 'completed' : 'active'}`;
    }    // Update investment details
    updateElement('investmentAmount', `${parseFloat(investmentData.amount).toFixed(2)} USDT`);
    updateElement('dailyReturn', `+${parseFloat(investmentData.daily_return).toFixed(2)} USDT`);
    updateElement('totalEarned', `${parseFloat(totalEarned).toFixed(2)} USDT`);
    updateElement('planDuration', `20 days`);

    // Update progress tracking
    updateElement('currentDay', `Day ${currentProgress}`);
    updateElement('totalDuration', 20);
    updateElement('progressPercentage', `${progressPercentage}%`);
    updateElement('dailyEarningProgress', `+${parseFloat(investmentData.daily_return).toFixed(2)} USDT`);
    
    // Update progress text
    updateElement('progressText', `${currentProgress} of 20 days completed`);
    
    // Update Quick Summary section
    updateElement('quickCurrentDay', `Day ${currentProgress}`);
    updateElement('quickRemainingDays', isCompleted ? 'Completed' : `${daysRemaining} days`);
    updateElement('quickTodayEarning', `+${parseFloat(investmentData.daily_return).toFixed(2)} USDT`);
    
    // Update timeline section
    updateElement('investmentStartDate', formatDate(new Date(investmentData.created_at)));
    updateElement('timeRemaining', isCompleted ? 'Completed' : `${daysRemaining} days`);
    updateElement('expectedTotalReturn', `${parseFloat(investmentData.total_return).toFixed(2)} USDT`);

    // Update progress bar
    const progressBar = document.getElementById('progressBar');
    if (progressBar) {
        progressBar.style.width = `${progressPercentage}%`;
    }    // Calculate completion date using provided completion_date from backend
    if (investmentData.completion_date) {
        const completionDate = new Date(investmentData.completion_date);
        updateElement('quickEndDate', isCompleted ? 'Completed' : formatDate(completionDate));
        updateElement('completionDate', isCompleted ? 'Plan completed' : formatDate(completionDate));
    } else {
        // Fallback calculation if completion_date is not provided
        const createdDate = new Date(investmentData.created_at);
        const completionDate = new Date(createdDate);
        completionDate.setDate(completionDate.getDate() + 20); // Always 20 days
        updateElement('quickEndDate', isCompleted ? 'Completed' : formatDate(completionDate));
        updateElement('completionDate', isCompleted ? 'Plan completed' : formatDate(completionDate));
    }    // Set next payout time for countdown
    if (!isCompleted) {
        // Calculate next payout time as 24 hours from now
        nextPayoutTime = new Date();
        nextPayoutTime.setTime(nextPayoutTime.getTime() + (24 * 60 * 60 * 1000)); // Add 24 hours in milliseconds
    }
}

async function loadEarningsHistory() {
    if (isLoadingEarnings) return; // Prevent multiple simultaneous calls
    
    try {
        isLoadingEarnings = true;
        const container = document.getElementById('earningsTableContainer');
        if (!container) return;

        // Show loading state
        container.innerHTML = `
            <div style="text-align: center; padding: 24px;">
                <div class="loading-spinner"></div>
                <p style="margin-top: 16px; color: #6c757d;">Loading earnings history...</p>
            </div>
        `;

        const response = await apiCall('get_investment_earnings', { id: INVESTMENT_ID });
        
        if (response.success && response.earnings && response.earnings.length > 0) {
            displayEarningsTable(response.earnings);
        } else {
            displayEmptyEarningsState();
        }
    } catch (error) {
        console.error('Error loading earnings history:', error);
        displayEarningsError();
    } finally {
        isLoadingEarnings = false;
    }
}

function displayEarningsTable(earnings) {
    const container = document.getElementById('earningsTableContainer');
    if (!container) return;

    container.innerHTML = `
        <div style="overflow-x: auto;">
            <table class="earnings-table">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Day</th>
                        <th>Amount Earned</th>
                        <th>Running Total</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    ${earnings.map((earning, index) => `
                        <tr>
                            <td class="earnings-date">${formatDate(new Date(earning.date))}</td>
                            <td>Day ${index + 1}</td>
                            <td class="earnings-amount">+${parseFloat(earning.amount).toFixed(2)} USDT</td>
                            <td class="earnings-amount">${parseFloat(earning.running_total).toFixed(2)} USDT</td>
                            <td>
                                <span style="
                                    padding: 4px 8px;
                                    border-radius: 12px;
                                    font-size: 0.8rem;
                                    font-weight: 600;
                                    background: ${earning.status === 'paid' ? '#d4edda' : '#fff3cd'};
                                    color: ${earning.status === 'paid' ? '#155724' : '#856404'};
                                ">
                                    ${earning.status === 'paid' ? '✅ Paid' : '⏳ Pending'}
                                </span>
                            </td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        </div>
    `;
}

function displayEmptyEarningsState() {
    const container = document.getElementById('earningsTableContainer');
    if (!container) return;

    container.innerHTML = `
        <div class="empty-state">
            <h4>No Earnings Yet</h4>
            <p>Earnings will appear here as they are processed daily.</p>
        </div>
    `;
}

function displayEarningsError() {
    const container = document.getElementById('earningsTableContainer');
    if (!container) return;

    container.innerHTML = `
        <div class="error-message">
            Failed to load earnings history. Please try refreshing the page.
        </div>
    `;
}

function initCountdownTimer() {
    if (!nextPayoutTime) {
        // Hide timer if investment is completed
        const timerSection = document.querySelector('.timer-section');
        if (timerSection) {
            timerSection.style.display = 'none';
        }
        return;
    }

    // Clear any existing interval
    if (countdownInterval) {
        clearInterval(countdownInterval);
    }

    // Update countdown every second
    countdownInterval = setInterval(updateCountdown, 1000);
    
    // Initial update
    updateCountdown();
    
    // Add manual refresh functionality
    addRefreshButton();
}

function addRefreshButton() {
    const timerSection = document.querySelector('.timer-section');
    if (timerSection && !timerSection.querySelector('.refresh-button')) {
        const refreshButton = document.createElement('button');
        refreshButton.className = 'refresh-button btn btn-outline';
        refreshButton.style.cssText = 'margin-top: 10px; padding: 4px 12px; font-size: 0.8rem;';
        refreshButton.textContent = '🔄 Check for Updates';
        refreshButton.onclick = async () => {
            refreshButton.disabled = true;
            refreshButton.textContent = 'Checking...';
            try {
                await loadInvestmentDetails();
                await loadEarningsHistory();
                showMessage('Data refreshed successfully', 'success');
            } catch (error) {
                showMessage('Failed to refresh data', 'error');
            } finally {
                refreshButton.disabled = false;
                refreshButton.textContent = '🔄 Check for Updates';
            }
        };
        timerSection.appendChild(refreshButton);
    }
}

function updateCountdown() {
    if (!nextPayoutTime) return;

    const now = new Date().getTime();
    const timeUntilPayout = nextPayoutTime.getTime() - now;

    if (timeUntilPayout <= 0) {        // Payout time has passed, reset to next 24-hour period
        clearInterval(countdownInterval);
        
        // Set next payout to 24 hours from now
        nextPayoutTime = new Date();
        nextPayoutTime.setTime(nextPayoutTime.getTime() + (24 * 60 * 60 * 1000)); // Add 24 hours in milliseconds
        
        // Restart timer immediately
        setTimeout(() => {
            initCountdownTimer();
        }, 1000);
        
        return;
    }

    // Calculate time remaining
    const hours = Math.floor(timeUntilPayout / (1000 * 60 * 60));
    const minutes = Math.floor((timeUntilPayout % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((timeUntilPayout % (1000 * 60)) / 1000);

    // Update display
    updateElement('hours', String(hours).padStart(2, '0'));
    updateElement('minutes', String(minutes).padStart(2, '0'));
    updateElement('seconds', String(seconds).padStart(2, '0'));
}

// Removed checkForNewPayout function to prevent infinite refresh loop
// The countdown timer now handles payout transitions without page refreshes

function updateElement(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = value;
    }
}

function formatDate(date) {
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

function showError(message) {
    const container = document.querySelector('.active-plan-container');
    if (container) {
        container.innerHTML = `
            <div class="error-message" style="margin: 48px auto; max-width: 600px; text-align: center;">
                <h3>Error</h3>
                <p>${message}</p>
                <div style="margin-top: 24px;">
                    <a href="dashboard.php" class="btn btn-primary">← Back to Dashboard</a>
                </div>
            </div>
        `;
    }
}

function showMessage(message, type = 'info') {
    // Create and show a temporary message
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}`;
    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 16px 24px;
        border-radius: 8px;
        background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#d1ecf1'};
        color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460'};
        border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : '#bee5eb'};
        z-index: 1000;
        max-width: 300px;
        word-wrap: break-word;
        animation: slideIn 0.3s ease;
    `;
    messageDiv.textContent = message;
    
    document.body.appendChild(messageDiv);
    
    // Remove message after 5 seconds
    setTimeout(() => {
        if (messageDiv.parentNode) {
            messageDiv.parentNode.removeChild(messageDiv);
        }
    }, 5000);
}

// API call function
async function apiCall(endpoint, data = null) {
    try {
        const response = await fetch('../ajax.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                action: endpoint,
                ...data
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error('API call failed:', error);
        return { success: false, error: error.message };
    }
}

// Footer menu initialization (assuming this function exists globally)
function initFooterMenu() {
    // This function should be implemented to initialize the footer menu
    // Based on other pages, it likely handles mobile navigation
    const footerMenu = document.querySelector('.footer-menu');
    if (footerMenu) {
        // Add any footer menu initialization logic here
    }
}

// Cleanup on page unload
window.addEventListener('beforeunload', function() {
    if (countdownInterval) {
        clearInterval(countdownInterval);
    }
});
