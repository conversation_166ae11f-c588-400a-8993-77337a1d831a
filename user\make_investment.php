<?php
// Include PSR-4 autoloader
require_once __DIR__ . '/../autoload.php';

use Frontend\Config\FrontendConfig;
use Frontend\Services\SessionService;

// Initialize PSR-4 configuration and session
FrontendConfig::init();
SessionService::init();

// Check if user is logged in
if (!SessionService::isAuthenticated()) {
    header('Location: ../index.php');
    exit;
}

$user = SessionService::getCurrentUser();

// Set variables for header
$pageTitle = 'Make Investment - TLS Wallet';
$currentPage = 'make_investment';
$basePath = '.';
$cssPath = 'css';
$additionalCSS = ['css/investment-redesign.css'];

// Include header
include '../includes/header.php';
?>

            <!-- Modern Compact Investment Page -->
            <div class="investment-page-redesign">
                <!-- Smart Header with Integrated Controls -->
                <div class="smart-header">
                    <div class="header-main">
                        <div class="page-title">
                            <h1>💰 Make Investment</h1>
                            <p>Start earning daily returns</p>
                        </div>
                        <div class="balance-widget">
                            <div class="balance-container">
                                <span class="balance-label">Available Balance</span>
                                <div class="balance-value">
                                    <span id="availableBalance" class="amount">Loading...</span>
                                    <span class="currency">USDT</span>
                                </div>
                                <div class="balance-status" id="balanceStatus">
                                    <span class="status-indicator"></span>
                                    <span class="status-text">Ready to invest</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Investment Flow Container -->
                <div class="investment-flow">
                    <!-- Step 1: Plan Selection -->
                    <div class="flow-step active" id="stepPlanSelection">
                        <div class="step-header">
                            <div class="step-indicator">
                                <span class="step-number">1</span>
                                <span class="step-title">Choose Plan</span>
                            </div>
                            <div class="step-info" id="planStepInfo">
                                <span class="plan-count" id="planCount">Loading...</span>
                            </div>
                        </div>

                        <div class="plans-grid" id="investmentPlansContainer">
                            <!-- Plans will be loaded dynamically -->
                            <div class="loading-state">
                                <div class="loading-spinner"></div>
                                <span>Loading investment plans...</span>
                            </div>
                        </div>
                    </div>

                    <!-- Step 2: Investment Details -->
                    <div class="flow-step" id="stepInvestmentDetails" style="display: none;">
                        <div class="step-header">
                            <div class="step-indicator">
                                <span class="step-number">2</span>
                                <span class="step-title">Investment Details</span>
                            </div>
                            <button class="step-back-btn" onclick="goBackToPlans()">
                                <i class="icon-arrow-left"></i> Change Plan
                            </button>
                        </div>                        <!-- Selected Plan Summary -->
                        <div class="selected-plan-card">
                            <div class="plan-preview">
                                <div class="plan-icon">📈</div>
                                <div class="plan-info">
                                    <h3 id="selectedPlanName">Basic Plan</h3>
                                    <div class="plan-metrics">
                                        <span class="metric">
                                            <span class="metric-value" id="selectedPlanRate">1.25%</span>
                                            <span class="metric-label">Daily</span>
                                        </span>
                                        <span class="metric">
                                            <span class="metric-value" id="selectedPlanDuration">20</span>
                                            <span class="metric-label">Days</span>
                                        </span>
                                        <span class="metric">
                                            <span class="metric-value" id="selectedPlanMinimum">300</span>
                                            <span class="metric-label">USDT Min</span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Investment Form -->
                        <div class="investment-form-card">
                            <form id="investmentForm" class="modern-form">
                                <input type="hidden" id="selectedPlan" name="selectedPlan" value="basic">

                                <!-- Amount Input Section -->
                                <div class="form-section">
                                    <div class="input-group">
                                        <label for="investAmount" class="input-label">
                                            Investment Amount
                                            <span class="label-hint">Must be exactly 300 USDT</span>
                                        </label>
                                        <div class="amount-input-container">
                                            <input type="number" id="investAmount" name="investAmount"
                                                   min="300" max="300" step="0.01" placeholder="300.00 USDT" required>
                    
                                            <button type="button" id="calculateBtn" class="calculate-btn">
                                                <span class="btn-icon">🧮</span>
                                                Calculate
                                            </button>
                                        </div>
                                        <div class="input-feedback">
                                            <div class="validation-message" id="amountValidation"></div>
                                            <div class="balance-check" id="balanceCheck">
                                                <span class="check-icon">💰</span>
                                                <span class="check-text">Balance: <span id="formUserBalance">0.00</span> USDT</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Investment Summary -->
                                <div class="summary-section" id="investmentSummary" style="display: none;">
                                    <div class="summary-header">
                                        <h4>📊 Investment Preview</h4>
                                        <span class="summary-badge">Calculated</span>
                                    </div>
                                    <div class="summary-cards">
                                        <div class="summary-card primary">
                                            <div class="card-icon">💵</div>
                                            <div class="card-content">
                                                <span class="card-label">Investment</span>
                                                <span class="card-value" id="summaryAmount">0 USDT</span>
                                            </div>
                                        </div>
                                        <div class="summary-card success">
                                            <div class="card-icon">📈</div>
                                            <div class="card-content">
                                                <span class="card-label">Daily Return</span>
                                                <span class="card-value" id="summaryDailyReturn">0 USDT</span>
                                            </div>
                                        </div>
                                        <div class="summary-card info">
                                            <div class="card-icon">🎯</div>
                                            <div class="card-content">
                                                <span class="card-label">Total Return</span>
                                                <span class="card-value" id="summaryTotalReturn">0 USDT</span>
                                            </div>
                                        </div>
                                        <div class="summary-card warning">
                                            <div class="card-icon">⏱️</div>
                                            <div class="card-content">
                                                <span class="card-label">Duration</span>
                                                <span class="card-value" id="summaryDuration">0 days</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Form Actions -->
                                <div class="form-actions">
                                    <button type="submit" id="investBtn" class="primary-action" disabled>
                                        <span class="btn-icon">🚀</span>
                                        <span class="btn-text">Confirm Investment</span>
                                        <div class="btn-loader">
                                            <div class="spinner"></div>
                                        </div>
                                    </button>
                                    <button type="button" id="backBtn" class="secondary-action" onclick="window.location.href='invest.php'">
                                        <span class="btn-icon">←</span>
                                        <span class="btn-text">Back to Overview</span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                        <!-- Insufficient Balance State -->
                        <div id="insufficientBalanceMsg" class="insufficient-balance-state" style="display: none;">
                            <div class="state-card warning">
                                <div class="state-icon">💳</div>
                                <div class="state-content">
                                    <h4>Insufficient Balance</h4>
                                    <p>You need more funds to make this investment</p>
                                    <div class="balance-needed" id="balanceNeeded">
                                        <span class="needed-label">Additional funds needed:</span>
                                        <span class="needed-amount" id="neededAmount">0 USDT</span>
                                    </div>
                                </div>
                                <div class="state-actions">
                                    <a href="deposit.php" class="primary-action">
                                        <span class="btn-icon">💰</span>
                                        <span class="btn-text">Add Funds</span>
                                    </a>
                                    <button type="button" class="secondary-action" onclick="window.location.href='invest.php'">
                                        <span class="btn-icon">←</span>
                                        <span class="btn-text">Back</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 3: Confirmation (Hidden, shown via JS) -->
                    <div class="flow-step" id="stepConfirmation" style="display: none;">
                        <div class="step-header">
                            <div class="step-indicator">
                                <span class="step-number">3</span>
                                <span class="step-title">Confirmation</span>
                            </div>
                        </div>
                        <div class="confirmation-content">
                            <!-- This will be populated by JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Quick Actions Sidebar -->
                <div class="quick-actions">
                    <div class="action-card">
                        <div class="action-icon">📊</div>
                        <div class="action-content">
                            <h4>View Active Investments</h4>
                            <p>Check your current investments</p>
                        </div>
                        <a href="invest.php" class="action-link">View →</a>
                    </div>
                    <div class="action-card">
                        <div class="action-icon">💰</div>
                        <div class="action-content">
                            <h4>Add More Funds</h4>
                            <p>Deposit USDT to your wallet</p>
                        </div>
                        <a href="deposit.php" class="action-link">Deposit →</a>
                    </div>
                    <div class="action-card">
                        <div class="action-icon">📈</div>
                        <div class="action-content">
                            <h4>Investment History</h4>
                            <p>View past investments</p>
                        </div>
                        <a href="transactions.php" class="action-link">History →</a>
                    </div>
                </div>
            </div>

            <!-- Modern Low Funds Modal -->
            <div id="lowFundsModal" class="modern-modal" style="display: none;">
                <div class="modal-overlay"></div>
                <div class="modal-container">
                    <div class="modal-content warning">
                        <div class="modal-header">
                            <div class="modal-icon">💳</div>
                            <div class="modal-title">
                                <h3>Insufficient Balance</h3>
                                <p>You need more funds to proceed</p>
                            </div>
                            <button type="button" class="modal-close" id="closeLowFundsModal">
                                <span>×</span>
                            </button>
                        </div>

                        <div class="modal-body">
                            <div class="balance-breakdown">
                                <div class="breakdown-item required">
                                    <div class="item-icon">🎯</div>
                                    <div class="item-content">
                                        <span class="item-label">Required Amount</span>
                                        <span class="item-value" id="modalRequiredAmount">0 USDT</span>
                                    </div>
                                </div>
                                <div class="breakdown-item available">
                                    <div class="item-icon">💰</div>
                                    <div class="item-content">
                                        <span class="item-label">Your Balance</span>
                                        <span class="item-value" id="modalUserBalance">0 USDT</span>
                                    </div>
                                </div>
                                <div class="breakdown-item deficit">
                                    <div class="item-icon">⚠️</div>
                                    <div class="item-content">
                                        <span class="item-label">Additional Needed</span>
                                        <span class="item-value" id="modalDeficitAmount">0 USDT</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="modal-footer">
                            <button type="button" class="primary-action" id="addFundsBtn">
                                <span class="btn-icon">💰</span>
                                <span class="btn-text">Add Funds</span>
                            </button>
                            <button type="button" class="secondary-action" id="cancelModalBtn">
                                <span class="btn-text">Cancel</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Compact Success Modal -->
            <div id="successModal" class="modern-modal compact" style="display: none;">
                <div class="modal-overlay"></div>
                <div class="modal-container compact">
                    <div class="modal-content success compact">
                        <div class="modal-header compact">
                            <div class="modal-icon">🎉</div>
                            <div class="modal-title">
                                <h3>Investment Created!</h3>
                                <p>Now active and earning returns</p>
                            </div>
                            <button type="button" class="modal-close" id="closeSuccessModal">
                                <span>×</span>
                            </button>
                        </div>

                        <div class="modal-body compact">
                            <div class="success-summary compact">
                                <div class="summary-row">
                                    <div class="summary-col">
                                        <span class="summary-label">Amount</span>
                                        <span class="summary-value" id="successAmount">0 USDT</span>
                                    </div>
                                    <div class="summary-col">
                                        <span class="summary-label">Plan</span>
                                        <span class="summary-value" id="successPlan">Basic</span>
                                    </div>
                                </div>
                                <div class="summary-row">
                                    <div class="summary-col">
                                        <span class="summary-label">ID</span>
                                        <span class="summary-value" id="successInvestmentId">#12345</span>
                                    </div>
                                    <div class="summary-col">
                                        <span class="summary-label">Balance</span>
                                        <span class="summary-value" id="successNewBalance">0 USDT</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="modal-footer compact">
                            <button type="button" class="primary-action compact" id="makeAnotherInvestmentBtn">
                                <span class="btn-icon">🚀</span>
                                <span class="btn-text">Invest Again</span>
                            </button>
                            <button type="button" class="secondary-action compact" id="viewDashboardBtn">
                                <span class="btn-icon">📊</span>
                                <span class="btn-text">Dashboard</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

<?php 
// Include footer
include '../includes/footer.php';
?>
    <script src="js/csrf-manager.js"></script>
    <script src="js/make_investment.js"></script>
</body>
</html>
