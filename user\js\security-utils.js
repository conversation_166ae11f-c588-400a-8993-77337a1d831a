/**
 * Security Utilities Library
 * Comprehensive security functions for XSS protection and secure DOM manipulation
 */

class SecurityUtils {
    /**
     * Escape HTML entities to prevent XSS attacks
     * @param {*} unsafe - Input to escape
     * @returns {string} - HTML-escaped string
     */
    static escapeHtml(unsafe) {
        if (typeof unsafe !== 'string') {
            if (unsafe === null || unsafe === undefined) return '';
            return String(unsafe);
        }
        
        return unsafe
            .replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;")
            .replace(/\//g, "&#x2F;")
            .replace(/`/g, "&#x60;")
            .replace(/=/g, "&#x3D;");
    }

    /**
     * Secure template literal replacement
     * @param {string} template - Template string with placeholders
     * @param {object} data - Data object
     * @returns {string} - Safely rendered template
     */
    static secureTemplate(template, data) {
        return template.replace(/\${([^}]+)}/g, (match, key) => {
            const value = this.getNestedProperty(data, key.trim());
            return this.escapeHtml(value);
        });
    }

    /**
     * Get nested property safely
     * @param {object} obj - Object to traverse
     * @param {string} path - Dot-notation path
     * @returns {*} - Property value or empty string
     */
    static getNestedProperty(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : '';
        }, obj);
    }

    /**
     * Safely set innerHTML with HTML escaping
     * @param {HTMLElement} element - Target element
     * @param {string} content - Content to set
     */
    static safeInnerHTML(element, content) {
        if (!element) return;
        element.innerHTML = this.escapeHtml(content);
    }

    /**
     * Safely set textContent (preferred method)
     * @param {HTMLElement} element - Target element
     * @param {string} content - Content to set
     */
    static safeTextContent(element, content) {
        if (!element) return;
        element.textContent = content || '';
    }

    /**
     * Create safe DOM element with escaped content
     * @param {string} tagName - Element tag name
     * @param {string} content - Element content
     * @param {object} attributes - Element attributes
     * @returns {HTMLElement} - Created element
     */
    static createSafeElement(tagName, content = '', attributes = {}) {
        const element = document.createElement(tagName);
        
        // Set content safely
        element.textContent = content;
        
        // Set attributes safely
        Object.keys(attributes).forEach(key => {
            if (attributes[key] !== null && attributes[key] !== undefined) {
                element.setAttribute(key, this.escapeHtml(String(attributes[key])));
            }
        });
        
        return element;
    }

    /**
     * Validate and sanitize user input
     * @param {string} input - User input
     * @param {string} type - Input type (email, number, text, etc.)
     * @returns {string} - Sanitized input
     */
    static sanitizeInput(input, type = 'text') {
        if (typeof input !== 'string') {
            input = String(input || '');
        }
        
        // Remove null bytes and control characters
        input = input.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
        
        switch (type) {
            case 'email':
                // Basic email sanitization
                return input.replace(/[<>"'&]/g, '').trim();
            
            case 'number':
                // Extract only numbers and decimal point
                return input.replace(/[^\d.-]/g, '');
            
            case 'currency':
                // Extract currency format
                return input.replace(/[^\d.,]/g, '');
            
            case 'alphanumeric':
                // Only letters and numbers
                return input.replace(/[^a-zA-Z0-9]/g, '');
            
            case 'text':
            default:
                // General text sanitization
                return input.trim();
        }
    }

    /**
     * Secure event handler wrapper
     * @param {Function} handler - Event handler function
     * @returns {Function} - Wrapped handler
     */
    static secureEventHandler(handler) {
        return function(event) {
            try {
                // Prevent XSS through event manipulation
                if (event && event.target && event.target.value) {
                    event.target.value = SecurityUtils.sanitizeInput(event.target.value);
                }
                return handler.call(this, event);
            } catch (error) {
                console.error('Secure event handler error:', error);
                return false;
            }
        };
    }

    /**
     * Validate URL to prevent javascript: and data: schemes
     * @param {string} url - URL to validate
     * @returns {boolean} - True if URL is safe
     */
    static isValidUrl(url) {
        if (!url || typeof url !== 'string') return false;
        
        // Block dangerous schemes
        const dangerousSchemes = ['javascript:', 'data:', 'vbscript:', 'file:', 'about:'];
        const lowercaseUrl = url.toLowerCase().trim();
        
        return !dangerousSchemes.some(scheme => lowercaseUrl.startsWith(scheme));
    }

    /**
     * Secure AJAX data preparation
     * @param {object} data - Data object
     * @returns {object} - Sanitized data object
     */
    static secureAjaxData(data) {
        const sanitized = {};
        
        Object.keys(data).forEach(key => {
            const value = data[key];
            if (typeof value === 'string') {
                sanitized[key] = this.sanitizeInput(value);
            } else if (typeof value === 'number') {
                sanitized[key] = value;
            } else if (typeof value === 'boolean') {
                sanitized[key] = value;
            } else if (value === null || value === undefined) {
                sanitized[key] = '';
            } else {
                sanitized[key] = this.sanitizeInput(String(value));
            }
        });
        
        return sanitized;
    }

    /**
     * Content Security Policy violation reporter
     * @param {string} violation - Violation details
     */
    static reportCSPViolation(violation) {
        console.warn('CSP Violation detected:', violation);
        
        // In production, send to security monitoring endpoint
        if (typeof fetch !== 'undefined') {
            fetch('/api/security/csp-violation', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    violation: violation,
                    timestamp: Date.now(),
                    userAgent: navigator.userAgent,
                    url: window.location.href
                })
            }).catch(err => console.error('Failed to report CSP violation:', err));
        }
    }

    /**
     * Initialize security features
     */
    static init() {
        // Set up CSP violation reporting
        if (typeof document !== 'undefined') {
            document.addEventListener('securitypolicyviolation', (e) => {
                this.reportCSPViolation({
                    blockedURI: e.blockedURI,
                    violatedDirective: e.violatedDirective,
                    originalPolicy: e.originalPolicy
                });
            });
        }

        // Global error handler for security issues
        if (typeof window !== 'undefined') {
            window.addEventListener('error', (e) => {
                if (e.message && e.message.includes('script')) {
                    console.warn('Potential script injection detected:', e.message);
                }
            });
        }
    }
}

// Auto-initialize on load
if (typeof document !== 'undefined') {
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => SecurityUtils.init());
    } else {
        SecurityUtils.init();
    }
}

// Export for both CommonJS and ES6 modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SecurityUtils;
}

// Global access
if (typeof window !== 'undefined') {
    window.SecurityUtils = SecurityUtils;
}
