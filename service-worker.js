self.addEventListener('install', event => {
  event.waitUntil(
    caches.open('tls-wallet-v1').then(cache => {
      return cache.addAll([
        '/',
        '/index.php',
        '/user/dashboard.php',
        '/user/deposit.php',
        '/user/invest.php',
        '/user/profile.php',
        '/user/transactions.php',
        '/user/css/style.css',
        '/user/css/dashboard.css',
        '/user/js/deposit.js',
        '/user/js/qr-generator.js',
        '/user/js/payment-details-mobile.js',
        // Add more assets as needed
      ]);
    })
  );
});

self.addEventListener('fetch', event => {
  const url = new URL(event.request.url);
  // Ignore API and AJAX requests
  if (url.pathname.startsWith('/api/') || url.pathname.startsWith('/ajax.php')) {
    return;
  }
  // Always fetch index.php from the network for navigation
  if (
    event.request.mode === 'navigate' ||
    url.pathname === '/index.php'
  ) {
    event.respondWith(fetch(event.request));
    return;
  }
  event.respondWith(
    caches.match(event.request).then(response => {
      return response || fetch(event.request);
    })
  );
});
