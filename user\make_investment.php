<?php
// Include PSR-4 autoloader
require_once __DIR__ . '/../autoload.php';

use Frontend\Config\FrontendConfig;
use Frontend\Services\SessionService;

// Initialize PSR-4 configuration and session
FrontendConfig::init();
SessionService::init();

// Check if user is logged in
if (!SessionService::isAuthenticated()) {
    header('Location: ../index.php');
    exit;
}

$user = SessionService::getCurrentUser();

// Set variables for header
$pageTitle = 'Make Investment - TLS Wallet';
$currentPage = 'make_investment';
$basePath = '.';
$cssPath = '../css';

// Include header
include '../includes/header.php';
?>

            <!-- Make Investment Content -->
            <div class="make-investment-page">
                <div class="page-header">
                    <h2>Make Investment</h2>
                    <p>Choose your investment plan and start earning daily returns</p>
                </div>

                <!-- Current Balance Display -->
                <div class="balance-overview">
                    <div class="balance-card">
                        <h3>Available Balance</h3>
                        <div class="balance-amount" id="availableBalance">Loading...</div>
                        <div class="balance-currency">USDT</div>
                    </div>
                </div>                <!-- Investment Plans -->
                <div class="investment-plans-section">
                    <h3>Choose Investment Plan</h3>
                    <div class="investment-plans" id="investmentPlansContainer">
                        <!-- Plans will be loaded dynamically from database -->
                        <div class="loading-spinner">Loading investment plans...</div>
                    </div>
                </div><!-- Investment Form -->
                <div class="card" id="investmentFormCard" style="display: none;">
                    <h3>Investment Details</h3>
                    <div class="investment-form-container">
                        <form id="investmentForm" class="investment-form">
                            <input type="hidden" id="selectedPlan" name="selectedPlan" value="basic">
                            
                            <!-- Plan Selection Summary -->
                            <div class="selected-plan-summary">
                                <div class="plan-summary-card">
                                    <div class="plan-summary-header">
                                        <h4 id="selectedPlanName">Basic Plan</h4>
                                        <span class="selected-plan-rate" id="selectedPlanRate">5.0% Daily</span>
                                    </div>
                                    <div class="plan-summary-details">
                                        <div class="detail-row">
                                            <span class="detail-label">Duration:</span>
                                            <span class="detail-value" id="selectedPlanDuration">20 days</span>
                                        </div>
                                        <div class="detail-row">
                                            <span class="detail-label">Minimum Amount:</span>
                                            <span class="detail-value" id="selectedPlanMinimum">300 USDT</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Balance Information -->
                            <div class="balance-info">
                                <div class="balance-info-card">
                                    <div class="balance-info-header">
                                        <h4>Your Balance</h4>
                                    </div>
                                    <div class="balance-info-amount">
                                        <span id="formUserBalance">0.00</span> USDT
                                    </div>
                                    <div class="balance-status" id="balanceStatus">
                                        <!-- Balance status will be updated dynamically -->
                                    </div>
                                </div>
                            </div>
                              <div class="form-group">
                                <label for="investAmount">Investment Amount (USDT)</label>
                                <input type="number" id="investAmount" name="investAmount" 
                                       min="300" max="300" step="0.01" placeholder="Enter amount (must be 300 USDT)" required>
                                <div class="input-help" id="amountHelp">Investment amount must be exactly 300 USDT</div>
                                <div class="amount-validation" id="amountValidation"></div>
                            </div>

                            <div class="form-group">
                                <button type="button" id="calculateBtn" class="btn btn-outline">Calculate Returns</button>
                            </div>

                            <div class="investment-summary" id="investmentSummary" style="display: none;">
                                <h4>Investment Summary</h4>
                                <div class="summary-details">
                                    <div class="summary-row">
                                        <span>Investment Amount:</span>
                                        <span id="summaryAmount">0 USDT</span>
                                    </div>
                                    <div class="summary-row">
                                        <span>Daily Return:</span>
                                        <span id="summaryDailyReturn">0 USDT</span>
                                    </div>
                                    <div class="summary-row">
                                        <span>Total Expected Return:</span>
                                        <span id="summaryTotalReturn">0 USDT</span>
                                    </div>
                                    <div class="summary-row">
                                        <span>Investment Duration:</span>
                                        <span id="summaryDuration">0 days</span>
                                    </div>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" id="investBtn" class="btn btn-primary" disabled>Confirm Investment</button>
                                <button type="button" id="backBtn" class="btn btn-secondary" onclick="window.location.href='invest.php'">Back to Overview</button>
                            </div>
                        </form>

                        <!-- Insufficient Balance Message -->
                        <div id="insufficientBalanceMsg" class="insufficient-balance" style="display: none;">
                            <div class="warning-card">
                                <h4>Insufficient Balance</h4>
                                <p>Your current balance is insufficient for this investment amount.</p>
                                <div class="warning-actions">
                                    <a href="deposit.php" class="btn btn-primary">Add More Funds</a>
                                    <button type="button" class="btn btn-secondary" onclick="window.location.href='invest.php'">Back to Overview</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Low Funds Modal -->
            <div id="lowFundsModal" class="modal" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>Insufficient Balance</h3>
                        <button type="button" class="modal-close" id="closeLowFundsModal">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="low-funds-content">
                            <div class="low-funds-icon">
                                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#dc3545" stroke-width="2">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <line x1="12" y1="8" x2="12" y2="12"></line>
                                    <line x1="12" y1="16" x2="12.01" y2="16"></line>
                                </svg>
                            </div>
                            <div class="low-funds-message">
                                <p><strong>Your balance is insufficient for this investment.</strong></p>
                                <div class="balance-comparison">
                                    <div class="comparison-row">
                                        <span class="comparison-label">Required Amount:</span>
                                        <span class="comparison-value required" id="modalRequiredAmount">0 USDT</span>
                                    </div>
                                    <div class="comparison-row">
                                        <span class="comparison-label">Your Balance:</span>
                                        <span class="comparison-value available" id="modalUserBalance">0 USDT</span>
                                    </div>
                                    <div class="comparison-row deficit">
                                        <span class="comparison-label">Amount Needed:</span>
                                        <span class="comparison-value deficit" id="modalDeficitAmount">0 USDT</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" id="addFundsBtn">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="12" y1="5" x2="12" y2="19"></line>
                                <line x1="5" y1="12" x2="19" y2="12"></line>
                            </svg>
                            Add More Funds
                        </button>                        <button type="button" class="btn btn-secondary" id="cancelModalBtn">Cancel</button>
                    </div>
                </div>
            </div>

            <!-- Success Modal -->
            <div id="successModal" class="modal" style="display: none;">
                <div class="modal-content success-modal">
                    <div class="modal-header success-header">
                        <h3>🎉 Investment Successful!</h3>
                        <button type="button" class="modal-close" id="closeSuccessModal">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="success-icon">
                            <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="#28a745" stroke-width="2">
                                <path d="m9 12 2 2 4-4"></path>
                                <circle cx="12" cy="12" r="9"></circle>
                            </svg>
                        </div>
                        <div class="success-message">
                            <p><strong>Your investment has been created successfully!</strong></p>
                            <div class="investment-details">
                                <div class="detail-row">
                                    <span class="detail-label">Investment Amount:</span>
                                    <span class="detail-value" id="successAmount">0 USDT</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">Plan:</span>
                                    <span class="detail-value" id="successPlan">Basic Plan</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">Investment ID:</span>
                                    <span class="detail-value" id="successInvestmentId">#12345</span>
                                </div>
                                <div class="detail-row">
                                    <span class="detail-label">New Balance:</span>
                                    <span class="detail-value" id="successNewBalance">0 USDT</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" id="makeAnotherInvestmentBtn">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="12" y1="5" x2="12" y2="19"></line>
                                <line x1="5" y1="12" x2="19" y2="12"></line>
                            </svg>
                            Make Another Investment
                        </button>
                        <button type="button" class="btn btn-secondary" id="viewDashboardBtn">View Dashboard</button>
                    </div>
                </div>
            </div>

<?php 
// Include footer
include '../includes/footer.php';
?>
    <script src="js/csrf-manager.js"></script>
    <script src="js/make_investment.js"></script>
</body>
</html>
