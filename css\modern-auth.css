/* Modern Authentication Styles */
:root {
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --primary-light: #818cf8;
    --secondary-color: #f59e0b;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;
    
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-tertiary: #f3f4f6;
    
    --border-color: #e5e7eb;
    --border-focus: #6366f1;
    
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
    font-size: 16px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Auth Wrapper */
.auth-wrapper {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    position: relative;
    overflow: hidden;
}

/* Background Shapes */
.bg-shapes {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 300px;
    height: 300px;
    top: -150px;
    right: -150px;
    animation-delay: 0s;
}

.shape-2 {
    width: 200px;
    height: 200px;
    bottom: -100px;
    left: -100px;
    animation-delay: 2s;
}

.shape-3 {
    width: 150px;
    height: 150px;
    top: 50%;
    left: -75px;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

/* Auth Container */
.auth-container {
    width: 100%;
    max-width: 420px;
    position: relative;
    z-index: 1;
}

/* Auth Header */
.auth-header {
    text-align: center;
    margin-bottom: 2rem;
    color: white;
}

.logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
}

.logo-icon {
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.logo-icon i {
    font-size: 1.5rem;
    color: white;
}

.auth-header h1 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 400;
    margin: 0;
}

/* Auth Card */
.auth-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: var(--radius-xl);
    padding: 2rem;
    box-shadow: var(--shadow-xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform var(--transition-normal);
}

.auth-card:hover {
    transform: translateY(-2px);
}

.card-header {
    text-align: center;
    margin-bottom: 2rem;
}

.card-header h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.card-header p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Form Styles */
.auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-wrapper input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 1rem;
    background: var(--bg-primary);
    transition: all var(--transition-fast);
    outline: none;
}

.input-wrapper input:focus {
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.input-wrapper input:focus + .input-focus-border {
    transform: scaleX(1);
}

.input-focus-border {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-color);
    transform: scaleX(0);
    transition: transform var(--transition-fast);
    border-radius: 0 0 var(--radius-md) var(--radius-md);
}

.input-icon {
    position: absolute;
    left: 1rem;
    color: var(--text-muted);
    font-size: 1rem;
    z-index: 1;
    transition: color var(--transition-fast);
}

.input-wrapper input:focus ~ .input-icon {
    color: var(--primary-color);
}

.password-toggle {
    position: absolute;
    right: 1rem;
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--radius-sm);
    transition: color var(--transition-fast);
    z-index: 1;
}

.password-toggle:hover {
    color: var(--primary-color);
}

.validation-icon {
    position: absolute;
    right: 1rem;
    font-size: 1rem;
    z-index: 1;
}

.validation-icon.valid {
    color: var(--success-color);
}

.validation-icon.invalid {
    color: var(--error-color);
}

.validation-message {
    font-size: 0.75rem;
    margin-top: 0.25rem;
    transition: all var(--transition-fast);
}

.validation-message.valid {
    color: var(--success-color);
}

.validation-message.invalid {
    color: var(--error-color);
}

.field-help {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-top: 0.25rem;
}

/* Password Strength Indicator */
.password-strength {
    margin-top: 0.75rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.strength-bar {
    width: 100%;
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.strength-fill {
    height: 100%;
    width: 0%;
    transition: all var(--transition-normal);
    border-radius: 2px;
}

.strength-fill.weak {
    width: 25%;
    background: var(--error-color);
}

.strength-fill.fair {
    width: 50%;
    background: var(--warning-color);
}

.strength-fill.good {
    width: 75%;
    background: var(--info-color);
}

.strength-fill.strong {
    width: 100%;
    background: var(--success-color);
}

.strength-text {
    font-size: 0.75rem;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
}

.password-requirements {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.requirement {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: var(--text-muted);
    transition: color var(--transition-fast);
}

.requirement i {
    width: 12px;
    font-size: 0.75rem;
}

.requirement.met {
    color: var(--success-color);
}

.requirement.met i:before {
    content: "\f00c";
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0.5rem 0;
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-secondary);
    cursor: pointer;
    user-select: none;
}

.checkbox-container input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
    background: var(--bg-primary);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark:after {
    content: "\f00c";
    font-family: "Font Awesome 6 Free";
    font-weight: 900;
    color: white;
    font-size: 0.75rem;
}

.forgot-link, .register-link, .login-link, .terms-link, .privacy-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.875rem;
    transition: color var(--transition-fast);
}

.forgot-link:hover, .register-link:hover, .login-link:hover, .terms-link:hover, .privacy-link:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* Button Styles */
.btn {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.875rem 1.5rem;
    border: none;
    border-radius: var(--radius-md);
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    overflow: hidden;
    min-height: 48px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-primary:active {
    transform: translateY(0);
}

.btn-text {
    transition: opacity var(--transition-fast);
}

.btn-loader {
    position: absolute;
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.btn.loading .btn-text {
    opacity: 0;
}

.btn.loading .btn-loader {
    opacity: 1;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Auth Footer */
.auth-footer {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.auth-footer p {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 1000;
    align-items: center;
    justify-content: center;
    padding: 1rem;
}

.modal.active {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: var(--radius-xl);
    padding: 2rem;
    width: 100%;
    max-width: 400px;
    box-shadow: var(--shadow-xl);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    color: var(--text-primary);
    background: var(--bg-secondary);
}

/* Message Container */
.message-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1001;
    max-width: 400px;
}

.message {
    padding: 1rem 1.5rem;
    border-radius: var(--radius-md);
    margin-bottom: 0.5rem;
    box-shadow: var(--shadow-lg);
    animation: messageSlideIn 0.3s ease-out;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.message.success {
    background: var(--success-color);
    color: white;
}

.message.error {
    background: var(--error-color);
    color: white;
}

.message.warning {
    background: var(--warning-color);
    color: white;
}

.message.info {
    background: var(--info-color);
    color: white;
}

/* Responsive Design */
@media (max-width: 480px) {
    .auth-wrapper {
        padding: 0.5rem;
    }
    
    .auth-card {
        padding: 1.5rem;
    }
    
    .auth-header h1 {
        font-size: 1.75rem;
    }
    
    .logo-container {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .form-options {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .message-container {
        top: 0.5rem;
        right: 0.5rem;
        left: 0.5rem;
        max-width: none;
    }
}

@media (max-width: 320px) {
    .auth-card {
        padding: 1rem;
    }
    
    .input-wrapper input {
        padding: 0.625rem 0.875rem 0.625rem 2.5rem;
        font-size: 0.875rem;
    }
    
    .btn {
        padding: 0.75rem 1.25rem;
        font-size: 0.875rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #f9fafb;
        --text-secondary: #d1d5db;
        --text-muted: #9ca3af;
        
        --bg-primary: #1f2937;
        --bg-secondary: #374151;
        --bg-tertiary: #4b5563;
        
        --border-color: #4b5563;
    }
    
    .auth-card {
        background: rgba(31, 41, 55, 0.95);
    }
    
    .input-wrapper input {
        background: var(--bg-secondary);
        color: var(--text-primary);
    }
    
    .password-strength {
        background: var(--bg-tertiary);
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-muted: #000000;
    }
    
    .input-wrapper input {
        border-width: 3px;
    }
    
    .btn {
        border: 2px solid transparent;
    }
    
    .btn-primary {
        border-color: var(--primary-dark);
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .shape {
        animation: none;
    }
}
