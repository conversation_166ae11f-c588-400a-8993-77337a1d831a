<?php
// Include PSR-4 autoloader
require_once __DIR__ . '/../autoload.php';

use Frontend\Config\FrontendConfig;
use Frontend\Services\SessionService;

// Initialize PSR-4 configuration and session
FrontendConfig::init();
SessionService::init();

// Check if user is logged in
if (!SessionService::isAuthenticated()) {
    header('Location: ../index.php');
    exit;
}

$user = SessionService::getCurrentUser();

// Set variables for header
$pageTitle = 'Profile - TLS Wallet';
$currentPage = 'profile';
$basePath = '.';
$cssPath = 'css';

// Include header
include '../includes/header.php';
?>    <!-- Modal Styles -->
    <style>
        /* Modal Overlay */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(4px);
            -webkit-backdrop-filter: blur(4px);
            animation: fadeIn 0.3s ease-out;
        }

        /* Modal Container */
        .modal-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            max-width: 450px;
            width: 90%;
            max-height: 90vh;
            overflow: hidden;
            animation: slideIn 0.3s ease-out;
        }

        /* Modal Content */
        .modal-content {
            display: flex;
            flex-direction: column;
        }

        /* Modal Header */
        .modal-header {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            padding: 20px 24px;
            text-align: center;
        }

        .modal-header h3 {
            margin: 0;
            font-size: 1.3rem;
            font-weight: 600;
        }

        /* Modal Body */
        .modal-body {
            padding: 24px;
            text-align: center;
        }

        .modal-body p {
            font-size: 1.1rem;
            color: #495057;
            margin: 0 0 20px 0;
            line-height: 1.5;
        }

        .logout-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 10px;
            color: #856404;
            font-size: 0.9rem;
        }

        .warning-icon {
            font-size: 1.2rem;
            flex-shrink: 0;
        }

        /* Modal Footer */
        .modal-footer {
            padding: 20px 24px;
            background: #f8f9fa;
            display: flex;
            gap: 12px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .modal-footer .btn {
            flex: 1;
            min-width: 120px;
            max-width: 160px;
        }

        /* Animations */
        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-30px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        /* Responsive Design */
        @media (max-width: 480px) {
            .modal-container {
                width: 95%;
                margin: 20px;
            }

            .modal-header {
                padding: 16px 20px;
            }

            .modal-header h3 {
                font-size: 1.2rem;
            }

            .modal-body {
                padding: 20px;
            }

            .modal-body p {
                font-size: 1rem;
            }

            .modal-footer {
                padding: 16px 20px;
                flex-direction: column;
            }

            .modal-footer .btn {
                flex: none;
                width: 100%;
                max-width: none;
            }
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .modal-overlay {
                background: rgba(0, 0, 0, 0.8);
            }

            .logout-warning {
                border-width: 2px;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            .modal-overlay,
            .modal-container {
                animation: none;
            }

            .modal-overlay {
                backdrop-filter: none;
                -webkit-backdrop-filter: none;
            }
        }
    </style>

            <!-- Profile Content -->
            <div class="deposit-page">
                <div class="page-header">
                    <h2>👤 User Profile</h2>
                    <p>Manage your account settings and preferences</p>
                    <div class="profile-header-status">
                        <span class="status-badge status-active">Active Account</span>
                    </div>
                </div>  

                <div class="card profile-card">                 <!-- Profile Information -->
                    <!-- <div class="profile-section account-info-section"> -->
                        <h3>📋 Account Information</h3>
                        <div class="profile-info">
                            <div class="profile-field">
                                <label>📧 Email Address:</label>
                                <span id="userEmail" class="field-value"><?php echo htmlspecialchars($user['email'] ?? ''); ?></span>
                            </div>
                            <div class="profile-field">
                                <label>🟢 Account Status:</label>
                                <span class="status-badge status-active">Active</span>
                            </div>
                            <div class="profile-field">
                                <label>🔗 Referral Code:</label>
                                <span id="userReferral" class="field-value"><?php echo htmlspecialchars($user['referral_code'] ?? 'N/A'); ?></span>
                            </div>
                            <div class="profile-field">
                                <label>🌐 Referral URL:</label>
                                <span id="userReferralUrl" class="field-value">
                                    <?php
                                    $referralCode = $user['referral_code'] ?? '';
                                    if ($referralCode) {
                                        $referralUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https" : "http") . "://" . $_SERVER['HTTP_HOST'] .'/index.php?by=' . urlencode($referralCode);
                                        echo htmlspecialchars($referralUrl);
                                    } else {
                                        echo 'N/A';
                                    }
                                    ?>
                                </span>
                            </div>
                        </div>
                    <!-- </div>                    Password Change -->
                    <!-- <div class="profile-section password-section"> -->
                        <div class="form-container">
                            <form id="changePasswordForm" class="password-form">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="currentPassword">Current Password</label>
                                        <input type="password" id="currentPassword" name="current_password" required class="form-control">
                                        <div class="field-help">Enter your current password</div>
                                    </div>
                                </div>
                                
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="newPassword">New Password</label>
                                        <input type="password" id="newPassword" name="new_password" required minlength="6" pattern="[A-Za-z0-9]+" title="Password must be 6+ characters, letters and numbers only" class="form-control">
                                        <div class="field-help">Minimum 6 characters, letters and numbers only</div>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="confirmPassword">Confirm New Password</label>
                                        <input type="password" id="confirmPassword" name="confirm_password" required class="form-control">
                                        <div class="field-help">Re-enter your new password</div>
                                    </div>
                                </div>
                                
                                <div class="password-requirements">
                                    <h4>Password Requirements:</h4>
                                    <ul>
                                        <li>At least 8 characters long</li>
                                        <li>Use a mix of letters, numbers, and symbols</li>
                                        <li>Avoid using personal information</li>
                                    </ul>
                                </div>
                                
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">🔄 Update Password</button>
                                    <button type="reset" class="btn btn-outline">↺ Reset Form</button>
                                </div>
                            </form>
                        </div>
                    <!-- </div>                    Security Settings -->
                    <div class="profile-section security-section">
                        <hr />
                        <h3>🛡️ Security Settings</h3>
                        <div class="security-info">
                            <div class="security-card">
                                <div class="security-icon">🔒</div>
                                <div class="security-content">
                                    <h4>Account Security Tips</h4>
                                    <p>Your account security is important. Here are some recommendations:</p>
                                    <ul class="security-list">
                                        <li>✅ Use a strong, unique password</li>
                                        <li>✅ Never share your login credentials</li>
                                        <li>✅ Log out when using shared computers</li>
                                        <li>✅ Contact support if you notice any suspicious activity</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Account Actions -->
                    <div class="profile-section actions-section">
                        <h3>⚙️ Account Actions</h3>
                        <div class="account-actions">
                            <a href="dashboard.php" class="btn btn-outline">
                                <span class="btn-icon">🏠</span>
                                Back to Dashboard
                            </a>                            <button type="button" class="btn btn-danger" onclick="showLogoutModal()">
                                <span class="btn-icon">🚪</span>
                                Logout
                            </button>
                        </div>
                    </div>
                </div>
            </div>

<!-- Logout Confirmation Modal -->
    <div id="logoutModal" class="modal-overlay" style="display: none;">
        <div class="modal-container">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>🚪 Confirm Logout</h3>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to logout from your account?</p>
                    <div class="logout-warning">
                        <span class="warning-icon">⚠️</span>
                        <span>You will need to login again to access your account.</span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline" onclick="hideLogoutModal()">
                        <span class="btn-icon">✕</span>
                        Cancel
                    </button>
                    <button type="button" class="btn btn-danger" onclick="confirmLogout()">
                        <span class="btn-icon">🚪</span>
                        Yes, Logout
                    </button>
                </div>
            </div>
        </div>
    </div>

<?php 
// Include footer
include '../includes/footer.php';
?>    <script>
        // Profile page specific JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            const changePasswordForm = document.getElementById('changePasswordForm');
            const newPasswordInput = document.getElementById('newPassword');
            const confirmPasswordInput = document.getElementById('confirmPassword');
            
            // Real-time password validation
            function validatePasswords() {
                const newPassword = newPasswordInput.value;
                const confirmPassword = confirmPasswordInput.value;
                
                // Clear previous validation styles
                newPasswordInput.classList.remove('error', 'success');
                confirmPasswordInput.classList.remove('error', 'success');
                
                // Validate new password
                if (newPassword.length >= 6 && /^[A-Za-z0-9]+$/.test(newPassword) && /[A-Za-z]/.test(newPassword) && /[0-9]/.test(newPassword)) {
                    newPasswordInput.classList.add('success');
                } else if (newPassword.length > 0) {
                    newPasswordInput.classList.add('error');
                }
                
                // Validate password confirmation
                if (confirmPassword.length > 0) {
                    if (newPassword === confirmPassword && newPassword.length >= 6) {
                        confirmPasswordInput.classList.add('success');
                    } else {
                        confirmPasswordInput.classList.add('error');
                    }
                }
            }
            
            // Add real-time validation
            if (newPasswordInput) {
                newPasswordInput.addEventListener('input', validatePasswords);
            }
            if (confirmPasswordInput) {
                confirmPasswordInput.addEventListener('input', validatePasswords);
            }
            
            if (changePasswordForm) {
                changePasswordForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    const currentPassword = document.getElementById('currentPassword').value;
                    const newPassword = newPasswordInput.value;
                    const confirmPassword = confirmPasswordInput.value;
                    
                    // Validate passwords match
                    if (newPassword !== confirmPassword) {
                        showMessage('🚫 New passwords do not match', 'error');
                        confirmPasswordInput.focus();
                        return;
                    }
                    
                    // Validate password length and format
                    if (newPassword.length < 6) {
                        showMessage('🚫 New password must be at least 6 characters long', 'error');
                        newPasswordInput.focus();
                        return;
                    }
                    
                    // Validate alphanumeric only
                    if (!/^[A-Za-z0-9]+$/.test(newPassword)) {
                        showMessage('🚫 Password must contain only letters and numbers', 'error');
                        newPasswordInput.focus();
                        return;
                    }
                    
                    // Validate contains at least one letter and one number
                    if (!/[A-Za-z]/.test(newPassword) || !/[0-9]/.test(newPassword)) {
                        showMessage('🚫 Password must contain at least one letter and one number', 'error');
                        newPasswordInput.focus();
                        return;
                    }
                    
                    // Validate current password
                    if (currentPassword.length === 0) {
                        showMessage('🚫 Please enter your current password', 'error');
                        document.getElementById('currentPassword').focus();
                        return;
                    }
                    
                    // Disable submit button during processing
                    const submitBtn = changePasswordForm.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;
                    submitBtn.disabled = true;
                    submitBtn.innerHTML = '⏳ Updating...';
                    
                    // Make API call to change password
                    apiCall('change_password', {
                        current_password: currentPassword,
                        new_password: newPassword
                    }).then(response => {
                        if (response.success) {
                            showMessage('✅ Password changed successfully', 'success');
                            changePasswordForm.reset();
                            validatePasswords(); // Clear validation styles
                        } else {
                            showMessage('🚫 ' + (response.message || 'Failed to change password'), 'error');
                        }
                    }).catch(error => {
                        console.error('Error changing password:', error);
                        showMessage('🚫 Error changing password. Please try again.', 'error');
                    }).finally(() => {
                        // Re-enable submit button
                        submitBtn.disabled = false;
                        submitBtn.innerHTML = originalText;
                    });
                });
            }
            
            // Add smooth scroll to top when message is shown
            function scrollToMessage() {
                const messageEl = document.getElementById('message');
                if (messageEl && messageEl.style.display !== 'none') {
                    messageEl.scrollIntoView({ behavior: 'smooth', block: 'start' });
                }
            }
            
            // Enhanced message display with auto-hide
            window.showMessage = function(message, type = 'info') {
                const messageEl = document.getElementById('message');
                if (messageEl) {
                    messageEl.textContent = message;
                    messageEl.className = `message ${type}`;
                    messageEl.style.display = 'block';
                    
                    // Scroll to message
                    setTimeout(scrollToMessage, 100);
                    
                    // Auto-hide after 5 seconds
                    setTimeout(() => {
                        messageEl.style.opacity = '0';
                        setTimeout(() => {
                            messageEl.style.display = 'none';
                            messageEl.style.opacity = '1';
                        }, 300);
                    }, 5000);
                }
            };
        });        // Enhanced API call with better error handling and CSRF protection
        async function apiCall(endpoint, data = {}) {
            try {
                // Get CSRF token for state-changing operations
                const csrfToken = await getCSRFToken();
                
                const response = await fetch('../ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: endpoint,
                        csrf_token: csrfToken,
                        ...data
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const result = await response.json();
                return result;
            } catch (error) {
                console.error('API call failed:', error);
                throw error;
            }
        }

        // Get CSRF token for API calls
        async function getCSRFToken() {
            try {
                const response = await fetch('../ajax.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        action: 'get_csrf_token',
                        context: 'user'
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const result = await response.json();
                if (result.success && result.csrf_token) {
                    return result.csrf_token;
                } else {
                    throw new Error('Failed to get CSRF token');
                }
            } catch (error) {
                console.error('Failed to get CSRF token:', error);
                throw error;
            }
        }
        
        // Add some interactive enhancements
        document.addEventListener('DOMContentLoaded', function() {
            // Add hover effects to profile fields
            const profileFields = document.querySelectorAll('.profile-field');
            profileFields.forEach(field => {
                field.addEventListener('mouseenter', function() {
                    this.style.borderColor = '#667eea';
                });
                field.addEventListener('mouseleave', function() {
                    this.style.borderColor = '#e9ecef';
                });
            });
              // Add click effect to buttons
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(btn => {
                btn.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });
        });

        // Logout Modal Functions
        function showLogoutModal() {
            const modal = document.getElementById('logoutModal');
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden'; // Prevent background scrolling
            
            // Focus trap for accessibility
            const focusableElements = modal.querySelectorAll('button');
            if (focusableElements.length > 0) {
                focusableElements[0].focus();
            }
            
            // Close modal on Escape key
            document.addEventListener('keydown', handleEscapeKey);
        }

        function hideLogoutModal() {
            const modal = document.getElementById('logoutModal');
            modal.style.display = 'none';
            document.body.style.overflow = ''; // Restore scrolling
            
            // Remove escape key listener
            document.removeEventListener('keydown', handleEscapeKey);
        }

        function confirmLogout() {
            // Show loading state
            const confirmBtn = document.querySelector('#logoutModal .btn-danger');
            const originalText = confirmBtn.innerHTML;
            confirmBtn.disabled = true;
            confirmBtn.innerHTML = '<span class="btn-icon">⏳</span>Logging out...';
            
            // Redirect to logout
            window.location.href = '../index.php?logout=1';
        }

        function handleEscapeKey(event) {
            if (event.key === 'Escape') {
                hideLogoutModal();
            }
        }

        // Close modal when clicking outside
        document.addEventListener('click', function(event) {
            const modal = document.getElementById('logoutModal');
            if (event.target === modal) {
                hideLogoutModal();
            }
        });
    </script>
</body>
</html>
