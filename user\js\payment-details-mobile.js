/**
 * Payment Details Mobile JavaScript
 * Enhanced mobile experience for deposit payment details
 */

document.addEventListener('DOMContentLoaded', function() {
    initMobilePaymentDetails();
});

/**
 * Initialize mobile-specific payment details functionality
 */
function initMobilePaymentDetails() {
    
    try {
        // Enhanced mobile copy functionality
        setupMobileCopyButtons();
        
        // Mobile-optimized QR code handling
        setupMobileQRCode();
        
        // Touch-friendly address interaction
        setupMobileAddressInteraction();
        
        // Mobile warning and confirmation dialogs
        setupMobileWarnings();
        
        // Responsive layout adjustments
        setupMobileLayoutAdjustments();
          // Mobile-specific deposit monitoring
        setupMobileDepositMonitoring();
    } catch (error) {
        console.error('Error initializing mobile payment details:', error);
    }
}

/**
 * Enhanced mobile copy functionality with better feedback
 */
function setupMobileCopyButtons() {
    const copyBtn = document.getElementById('copyPaymentBtn');
    const addressElement = document.getElementById('paymentAddress');
    
    if (copyBtn && addressElement) {
        // Remove any existing event listeners
        copyBtn.replaceWith(copyBtn.cloneNode(true));
        const newCopyBtn = document.getElementById('copyPaymentBtn');
        
        newCopyBtn.addEventListener('click', async function(e) {
            e.preventDefault();
            
            const address = addressElement.textContent.trim();
            if (!address || address === 'Loading wallet address...' || address === 'Create a wallet first') {
                showMobileToast('❌ No address available to copy', 'error');
                return;
            }
            
            try {
                // Try modern clipboard API first
                if (navigator.clipboard && window.isSecureContext) {
                    await navigator.clipboard.writeText(address);
                    showMobileSuccess(newCopyBtn, 'Address copied!');
                } else {
                    // Fallback for older browsers or non-HTTPS
                    fallbackCopyToClipboard(address);
                    showMobileSuccess(newCopyBtn, 'Address copied!');
                }
                
                // Mobile haptic feedback
                triggerHapticFeedback('light');
                
            } catch (error) {
                console.error('Copy failed:', error);
                showMobileToast('❌ Failed to copy address', 'error');
            }
        });
        
        // Add long-press functionality for mobile
        let longPressTimer;
        newCopyBtn.addEventListener('touchstart', function(e) {
            longPressTimer = setTimeout(() => {
                // Long press - show address details
                showAddressDetails(addressElement.textContent.trim());
                triggerHapticFeedback('medium');
            }, 800);
        });
        
        newCopyBtn.addEventListener('touchend', function(e) {
            clearTimeout(longPressTimer);
        });
        
        newCopyBtn.addEventListener('touchmove', function(e) {
            clearTimeout(longPressTimer);
        });
    }
}

/**
 * Fallback copy method for older browsers
 */
function fallbackCopyToClipboard(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        document.execCommand('copy');
    } catch (err) {
        console.error('Fallback copy failed:', err);
        throw err;
    } finally {
        document.body.removeChild(textArea);
    }
}

/**
 * Show mobile-optimized success feedback
 */
function showMobileSuccess(button, message) {
    const originalText = button.innerHTML;
    const originalClass = button.className;
    
    button.innerHTML = `✅ ${message}`;
    button.className = originalClass + ' success-state';
    button.style.transform = 'scale(0.95)';
    
    setTimeout(() => {
        button.style.transform = 'scale(1)';
    }, 100);
    
    setTimeout(() => {
        button.innerHTML = originalText;
        button.className = originalClass;
    }, 2000);
}

/**
 * Mobile-optimized QR code handling
 */
function setupMobileQRCode() {
    const qrContainer = document.getElementById('paymentQR');
    
    if (qrContainer) {
        // Add tap-to-enlarge functionality for mobile
        qrContainer.addEventListener('click', function(e) {
            if (window.innerWidth <= 768) {
                enlargeQRCode(qrContainer);
            }
        });
        
        // Add pinch-to-zoom detection
        let initialDistance = 0;
        let scale = 1;
        
        qrContainer.addEventListener('touchstart', function(e) {
            if (e.touches.length === 2) {
                e.preventDefault();
                initialDistance = Math.hypot(
                    e.touches[0].pageX - e.touches[1].pageX,
                    e.touches[0].pageY - e.touches[1].pageY
                );
            }
        });
        
        qrContainer.addEventListener('touchmove', function(e) {
            if (e.touches.length === 2) {
                e.preventDefault();
                const currentDistance = Math.hypot(
                    e.touches[0].pageX - e.touches[1].pageX,
                    e.touches[0].pageY - e.touches[1].pageY
                );
                
                if (initialDistance > 0) {
                    scale = currentDistance / initialDistance;
                    qrContainer.style.transform = `scale(${Math.min(Math.max(scale, 0.5), 3)})`;
                }
            }
        });
        
        qrContainer.addEventListener('touchend', function(e) {
            if (e.touches.length < 2) {
                qrContainer.style.transform = 'scale(1)';
                initialDistance = 0;
                scale = 1;
            }
        });
    }
}

/**
 * Enlarge QR code in mobile modal
 */
function enlargeQRCode(qrContainer) {
    const modal = document.createElement('div');
    modal.className = 'qr-modal-mobile';
    modal.innerHTML = `
        <div class="qr-modal-content">
            <div class="qr-modal-header">
                <h3>📱 Scan QR Code</h3>
                <button class="qr-modal-close">✕</button>
            </div>
            <div class="qr-modal-body">
                ${qrContainer.innerHTML}
                <p>📲 Scan with your TRON wallet app</p>
            </div>
        </div>
    `;
    
    // Add modal styles
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        padding: 20px;
    `;
    
    modal.querySelector('.qr-modal-content').style.cssText = `
        background: white;
        border-radius: 12px;
        max-width: 90%;
        max-height: 90%;
        text-align: center;
        animation: slideInUp 0.3s ease-out;
    `;
    
    modal.querySelector('.qr-modal-close').style.cssText = `
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        padding: 10px;
        float: right;
    `;
    
    document.body.appendChild(modal);
    triggerHapticFeedback('light');
    
    // Close modal functionality
    const closeModal = () => {
        modal.remove();
    };
    
    modal.querySelector('.qr-modal-close').addEventListener('click', closeModal);
    modal.addEventListener('click', function(e) {
        if (e.target === modal) closeModal();
    });
}

/**
 * Touch-friendly address interaction
 */
function setupMobileAddressInteraction() {
    const addressElement = document.getElementById('paymentAddress');
    
    if (addressElement) {
        // Add tap feedback
        addressElement.addEventListener('touchstart', function(e) {
            this.style.transform = 'scale(0.98)';
            this.style.backgroundColor = '#e9ecef';
        });
        
        addressElement.addEventListener('touchend', function(e) {
            this.style.transform = 'scale(1)';
            setTimeout(() => {
                this.style.backgroundColor = '';
            }, 200);
        });
        
        // Double-tap to copy
        let lastTap = 0;
        addressElement.addEventListener('touchend', function(e) {
            const currentTime = new Date().getTime();
            const tapLength = currentTime - lastTap;
            if (tapLength < 500 && tapLength > 0) {
                // Double tap detected
                const copyBtn = document.getElementById('copyPaymentBtn');
                if (copyBtn) {
                    copyBtn.click();
                }
            }
            lastTap = currentTime;
        });
    }
}

/**
 * Mobile warning and confirmation dialogs
 */
function setupMobileWarnings() {
    // Enhanced mobile-friendly warning display
    const warningSteps = document.querySelectorAll('.instruction-step.warning-step');
    
    warningSteps.forEach(step => {
        // Make warning more prominent on mobile
        if (window.innerWidth <= 768) {
            step.style.animation = 'pulseWarning 2s infinite';
        }
        
        // Add tap acknowledgment
        step.addEventListener('click', function() {
            this.style.animation = 'none';
            this.style.borderColor = '#28a745';
            
            // Show acknowledgment
            const ackIcon = document.createElement('div');
            ackIcon.innerHTML = '✅ Acknowledged';
            ackIcon.style.cssText = `
                position: absolute;
                top: 10px;
                right: 10px;
                background: #28a745;
                color: white;
                padding: 5px 10px;
                border-radius: 5px;
                font-size: 0.8rem;
                animation: fadeInOut 3s ease-out;
            `;
            
            this.style.position = 'relative';
            this.appendChild(ackIcon);
            
            setTimeout(() => {
                if (ackIcon.parentNode) {
                    ackIcon.remove();
                }
            }, 3000);
            
            triggerHapticFeedback('light');
        });
    });
}

/**
 * Responsive layout adjustments for mobile
 */
function setupMobileLayoutAdjustments() {
    // Adjust layout based on screen orientation
    const handleOrientationChange = () => {
        setTimeout(() => {
            const paymentDetails = document.getElementById('paymentDetails');
            if (paymentDetails && window.innerWidth <= 768) {
                if (window.innerHeight < window.innerWidth) {
                    // Landscape mode
                    paymentDetails.style.padding = '10px';
                } else {
                    // Portrait mode
                    paymentDetails.style.padding = '20px';
                }
            }
        }, 100);
    };
    
    window.addEventListener('orientationchange', handleOrientationChange);
    window.addEventListener('resize', handleOrientationChange);
    
    // Initial setup
    handleOrientationChange();
}

/**
 * Mobile-specific deposit monitoring
 */
function setupMobileDepositMonitoring() {
    // Check if we're in payment monitoring mode
    if (typeof isPaymentMonitoringActive !== 'undefined' && isPaymentMonitoringActive) {
        // Show mobile-optimized monitoring indicator
        showMobileMonitoringStatus();
        
        // Add mobile-specific page visibility handling
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                showMobileToast('⚠️ Keep this page open for payment monitoring', 'warning');
            }
        });
        
        // Prevent mobile from sleeping during monitoring
        if ('wakeLock' in navigator) {
            requestWakeLock();
        }
    }
}

/**
 * Show mobile monitoring status
 */
function showMobileMonitoringStatus() {
    const statusBar = document.createElement('div');
    statusBar.id = 'mobile-monitoring-status';
    statusBar.innerHTML = `
        <div class="monitoring-indicator">
            <div class="pulse-dot"></div>
            <span>Monitoring for payment...</span>
        </div>
    `;
    
    statusBar.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        background: linear-gradient(135deg, #28a745, #20c997);
        color: white;
        padding: 10px;
        text-align: center;
        z-index: 1000;
        font-size: 0.9rem;
        font-weight: 600;
    `;
    
    document.body.insertBefore(statusBar, document.body.firstChild);
    
    // Adjust main content margin
    const main = document.querySelector('main') || document.querySelector('.container');
    if (main) {
        main.style.marginTop = '50px';
    }
}

/**
 * Request wake lock to prevent screen from sleeping
 */
async function requestWakeLock() {
    try {
        const wakeLock = await navigator.wakeLock.request('screen');
        
        wakeLock.addEventListener('release', () => {
        });
        
        return wakeLock;
    } catch (err) {
        console.error('Wake lock failed:', err);
    }
}

/**
 * Mobile haptic feedback
 */
function triggerHapticFeedback(intensity = 'light') {
    if ('vibrate' in navigator) {
        switch (intensity) {
            case 'light':
                navigator.vibrate(50);
                break;
            case 'medium':
                navigator.vibrate([100, 50, 100]);
                break;
            case 'heavy':
                navigator.vibrate([200, 100, 200]);
                break;
        }
    }
}

/**
 * Show mobile-optimized toast message
 */
function showMobileToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `mobile-toast mobile-toast-${type}`;
    toast.textContent = message;
    
    toast.style.cssText = `
        position: fixed;
        bottom: 80px;
        left: 20px;
        right: 20px;
        background: ${type === 'error' ? '#dc3545' : type === 'warning' ? '#ffc107' : '#28a745'};
        color: ${type === 'warning' ? '#000' : '#fff'};
        padding: 15px;
        border-radius: 8px;
        text-align: center;
        font-weight: 600;
        z-index: 9999;
        animation: slideInUp 0.3s ease-out;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    `;
    
    document.body.appendChild(toast);
    
    setTimeout(() => {
        toast.style.animation = 'slideOutDown 0.3s ease-in';
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 300);
    }, 3000);
}

/**
 * Show address details modal
 */
function showAddressDetails(address) {
    if (!address || address === 'Loading wallet address...' || address === 'Create a wallet first') {
        showMobileToast('❌ No address available', 'error');
        return;
    }
    
    const modal = document.createElement('div');
    modal.className = 'address-details-modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>📍 Address Details</h3>
                <button class="modal-close">✕</button>
            </div>
            <div class="modal-body">
                <div class="address-info">
                    <p><strong>Network:</strong> TRON (TRC-20)</p>
                    <p><strong>Currency:</strong> USDT</p>
                    <div class="address-display">
                        <label>Your Deposit Address:</label>
                        <div class="address-text">${address}</div>
                    </div>
                    <div class="address-actions">
                        <button class="btn-copy-modal">📋 Copy Address</button>
                        <button class="btn-share">📤 Share</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Add modal styles
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        padding: 20px;
    `;
    
    document.body.appendChild(modal);
    
    // Modal functionality
    const closeModal = () => modal.remove();
    
    modal.querySelector('.modal-close').addEventListener('click', closeModal);
    modal.addEventListener('click', function(e) {
        if (e.target === modal) closeModal();
    });
    
    // Copy functionality
    modal.querySelector('.btn-copy-modal').addEventListener('click', async function() {
        try {
            await navigator.clipboard.writeText(address);
            showMobileToast('✅ Address copied!', 'success');
            triggerHapticFeedback('light');
        } catch (err) {
            showMobileToast('❌ Failed to copy', 'error');
        }
    });
    
    // Share functionality
    modal.querySelector('.btn-share').addEventListener('click', function() {
        if (navigator.share) {
            navigator.share({
                title: 'TRON Deposit Address',
                text: `My TRON USDT deposit address: ${address}`
            });
        } else {
            // Fallback - copy to clipboard
            navigator.clipboard.writeText(address);
            showMobileToast('✅ Address copied for sharing!', 'success');
        }
    });
}

// Add required CSS animations
const mobileStyles = document.createElement('style');
mobileStyles.textContent = `
    @keyframes slideInUp {
        from {
            transform: translateY(100%);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutDown {
        from {
            transform: translateY(0);
            opacity: 1;
        }
        to {
            transform: translateY(100%);
            opacity: 0;
        }
    }
    
    @keyframes pulseWarning {
        0%, 100% {
            border-color: #dc3545;
            box-shadow: 0 0 0 rgba(220, 53, 69, 0);
        }
        50% {
            border-color: #dc3545;
            box-shadow: 0 0 20px rgba(220, 53, 69, 0.3);
        }
    }
    
    @keyframes fadeInOut {
        0%, 100% { opacity: 0; }
        25%, 75% { opacity: 1; }
    }
    
    .pulse-dot {
        display: inline-block;
        width: 8px;
        height: 8px;
        background: white;
        border-radius: 50%;
        margin-right: 8px;
        animation: pulse 1.5s infinite;
    }
    
    @keyframes pulse {
        0%, 100% { opacity: 1; transform: scale(1); }
        50% { opacity: 0.5; transform: scale(1.2); }
    }
    
    .success-state {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    }
    
    /* Modal styles for mobile */
    .modal-content {
        background: white;
        border-radius: 12px;
        max-width: 95%;
        max-height: 90%;
        overflow-y: auto;
    }
    
    .modal-header {
        padding: 20px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .modal-body {
        padding: 20px;
    }
    
    .address-display {
        margin: 15px 0;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
    }
    
    .address-text {
        font-family: monospace;
        font-size: 12px;
        word-break: break-all;
        background: white;
        padding: 10px;
        border-radius: 4px;
        margin-top: 10px;
    }
    
    .address-actions {
        display: flex;
        gap: 10px;
        margin-top: 15px;
    }
    
    .address-actions button {
        flex: 1;
        padding: 12px;
        border: none;
        border-radius: 6px;
        background: #007bff;
        color: white;
        font-weight: 600;
        cursor: pointer;
    }
    
    .btn-share {
        background: #28a745 !important;
    }
    
    .modal-close {
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: #6c757d;
    }
`;

document.head.appendChild(mobileStyles);
