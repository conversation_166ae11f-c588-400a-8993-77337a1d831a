/* ========================================
   MODERN INVESTMENT PAGE REDESIGN
   ======================================== */

/* CSS Variables for Consistent Theming */
:root {
    --primary-color: #667eea;
    --primary-dark: #4f46e5;
    --primary-light: #818cf8;
    --secondary-color: #f59e0b;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;
    
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-tertiary: #f3f4f6;
    
    --border-color: #e5e7eb;
    --border-focus: #667eea;
    
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
}

/* Reset and Base Styles */
.investment-page-redesign {
    padding: var(--spacing-md);
    max-width: 1400px;
    margin: 0 auto;
    background: var(--bg-secondary);
    min-height: calc(100vh - 140px);
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: var(--spacing-xl);
}

/* Smart Header */
.smart-header {
    grid-column: 1 / -1;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    color: white;
    box-shadow: var(--shadow-lg);
    margin-bottom: var(--spacing-lg);
}

.header-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-lg);
}

.page-title h1 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0 0 var(--spacing-xs) 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.page-title p {
    margin: 0;
    opacity: 0.9;
    font-size: 1.1rem;
}

.balance-widget {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
    min-width: 250px;
}

.balance-container {
    text-align: center;
}

.balance-label {
    font-size: 0.875rem;
    opacity: 0.8;
    display: block;
    margin-bottom: var(--spacing-xs);
}

.balance-value {
    display: flex;
    align-items: baseline;
    justify-content: center;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-sm);
}

.balance-value .amount {
    font-size: 2rem;
    font-weight: 700;
    line-height: 1;
}

.balance-value .currency {
    font-size: 1rem;
    opacity: 0.9;
}

.balance-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
    font-size: 0.875rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #10b981;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Investment Flow */
.investment-flow {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.flow-step {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
}

.flow-step.active {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-lg);
}

.step-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
}

.step-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.step-number {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 0.875rem;
}

.step-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.step-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.plan-count {
    background: var(--bg-tertiary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.step-back-btn {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-secondary);
    font-size: 0.875rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.step-back-btn:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Plans Grid */
.plans-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
}

.plan-card {
    background: var(--bg-primary);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    cursor: pointer;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.plan-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.plan-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.plan-card:hover::before {
    transform: scaleX(1);
}

.plan-card.active {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
    box-shadow: var(--shadow-lg);
}

.plan-card.active::before {
    transform: scaleX(1);
}

.plan-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.plan-name {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
}

.plan-rate-container {
    text-align: right;
}

.plan-rate {
    font-size: 1.5rem;
    font-weight: 700;
    color: #d3d3d3;
    line-height: 1;
}

.plan-period {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.plan-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.plan-badge {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    background: var(--secondary-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
}

.plan-badge.coming-soon {
    background: var(--text-muted);
}

/* Loading State */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Selected Plan Card */
.selected-plan-card {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
    border: 2px solid var(--primary-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.plan-preview {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
}

.plan-icon {
    font-size: 2.5rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    border-radius: var(--radius-lg);
    color: white;
}

.plan-info h3 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.plan-metrics {
    display: flex;
    gap: var(--spacing-lg);
}

.metric {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.metric-value {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
}

.metric-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

/* Investment Form Card */
.investment-form-card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    border: 1px solid var(--border-color);
}

.modern-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.form-section {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.input-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.input-label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.875rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.label-hint {
    font-weight: 400;
    color: var(--text-secondary);
    font-size: 0.75rem;
}

.amount-input-container {
    display: flex;
    align-items: center;
    background: var(--bg-primary);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: border-color var(--transition-fast);
}

.amount-input-container:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.amount-input-container input {
    flex: 1;
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 1.125rem;
    font-weight: 600;
    background: transparent;
    outline: none;
    color: var(--text-primary);
}

.amount-input-container input::placeholder {
    color: var(--text-muted);
    font-weight: 400;
}

.input-currency {
    padding: var(--spacing-md);
    background: var(--bg-tertiary);
    color: var(--text-secondary);
    font-weight: 600;
    font-size: 0.875rem;
    border-left: 1px solid var(--border-color);
}

.calculate-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-md) var(--spacing-lg);
    font-weight: 600;
    cursor: pointer;
    transition: background-color var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.875rem;
}

.calculate-btn:hover {
    background: var(--primary-dark);
}

.input-feedback {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.validation-message {
    font-size: 0.875rem;
    font-weight: 500;
}

.validation-message.valid {
    color: var(--success-color);
}

.validation-message.invalid {
    color: var(--error-color);
}

.balance-check {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.875rem;
    color: var(--text-secondary);
    padding: var(--spacing-sm);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
}

.check-icon {
    font-size: 1rem;
}

/* Summary Section */
.summary-section {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
}

.summary-header h4 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.summary-badge {
    background: var(--success-color);
    color: white;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
}

.summary-card {
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    transition: transform var(--transition-fast);
}

.summary-card:hover {
    transform: translateY(-1px);
}

.summary-card.primary {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
}

.summary-card.success {
    border-color: var(--success-color);
    background: linear-gradient(135deg, #f0fdf4 0%, #ecfdf5 100%);
}

.summary-card.info {
    border-color: var(--info-color);
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.summary-card.warning {
    border-color: var(--warning-color);
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}

.card-icon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.8);
}

.card-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.card-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.card-value {
    font-size: 1rem;
    font-weight: 700;
    color: var(--text-primary);
}

/* Form Actions */
.form-actions {
    display: flex;
    gap: var(--spacing-md);
    margin-top: var(--spacing-lg);
}

.primary-action, .secondary-action {
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--spacing-md) var(--spacing-xl);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1rem;
    position: relative;
    overflow: hidden;
}

.primary-action {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    flex: 2;
    box-shadow: var(--shadow-md);
}

.primary-action:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.primary-action:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.secondary-action {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    flex: 1;
}

.secondary-action:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.btn-icon {
    font-size: 1.125rem;
}

.btn-text {
    transition: opacity var(--transition-fast);
}

.btn-loader {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity var(--transition-fast);
}

.primary-action.loading .btn-text {
    opacity: 0;
}

.primary-action.loading .btn-loader {
    opacity: 1;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Insufficient Balance State */
.insufficient-balance-state {
    margin-top: var(--spacing-lg);
}

.state-card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    border: 1px solid var(--border-color);
    text-align: center;
}

.state-card.warning {
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
    border-color: var(--warning-color);
}

.state-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.state-content h4 {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.state-content p {
    margin: 0 0 var(--spacing-lg) 0;
    color: var(--text-secondary);
}

.balance-needed {
    background: var(--bg-primary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    border: 1px solid var(--border-color);
}

.needed-label {
    display: block;
    font-size: 0.875rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.needed-amount {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--error-color);
}

.state-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
}

/* Quick Actions Sidebar */
.quick-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.action-card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.action-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.action-icon {
    font-size: 2rem;
    margin-bottom: var(--spacing-md);
    display: block;
}

.action-content h4 {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.action-content p {
    margin: 0;
    font-size: 0.875rem;
    color: var(--text-secondary);
    line-height: 1.4;
}

.action-link {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    color: var(--primary-color);
    font-weight: 600;
    font-size: 0.875rem;
    text-decoration: none;
    transition: color var(--transition-fast);
}

.action-link:hover {
    color: var(--primary-dark);
}

/* Modern Modal System */
.modern-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-lg);
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-container {
    position: relative;
    z-index: 1001;
    width: 100%;
    max-width: 500px;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
}

.modal-content.warning {
    border-top: 4px solid var(--warning-color);
}

.modal-content.success {
    border-top: 4px solid var(--success-color);
}

.modal-header {
    padding: var(--spacing-xl);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    border-bottom: 1px solid var(--border-color);
    position: relative;
}

.modal-icon {
    font-size: 2rem;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-lg);
    background: var(--bg-secondary);
}

.modal-title h3 {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.modal-title p {
    margin: 0;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.modal-close {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    width: 32px;
    height: 32px;
    border: none;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-xl);
}

.modal-footer {
    padding: var(--spacing-xl);
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: var(--spacing-md);
}

/* Modal Content Styles */
.balance-breakdown, .success-summary {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

/* Compact Modal Styles */
.modern-modal.compact {
    padding: var(--spacing-sm);
}

.modal-container.compact {
    max-width: 400px;
    max-height: 90vh;
    overflow-y: auto;
}

.modal-content.compact {
    border-radius: var(--radius-lg);
}

.modal-header.compact {
    padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md) var(--spacing-lg);
}

.modal-header.compact .modal-title h3 {
    font-size: 1.125rem;
    margin-bottom: var(--spacing-xs);
}

.modal-header.compact .modal-title p {
    font-size: 0.8rem;
}

.modal-body.compact {
    padding: 0 var(--spacing-lg) var(--spacing-md) var(--spacing-lg);
}

.modal-footer.compact {
    padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg) var(--spacing-lg);
    gap: var(--spacing-sm);
}

/* Compact Success Summary */
.success-summary.compact {
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    gap: var(--spacing-sm);
}

.summary-row {
    display: flex;
    gap: var(--spacing-md);
}

.summary-col {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: var(--spacing-sm);
    background: var(--bg-primary);
    border-radius: var(--radius-sm);
    border: 1px solid var(--border-color);
}

.summary-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    font-weight: 500;
}

.summary-value {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Compact Action Buttons */
.primary-action.compact,
.secondary-action.compact {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.875rem;
    border-radius: var(--radius-md);
}

.primary-action.compact .btn-text,
.secondary-action.compact .btn-text {
    font-size: 0.875rem;
}

.primary-action.compact .btn-icon,
.secondary-action.compact .btn-icon {
    font-size: 1rem;
}

.breakdown-item, .summary-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.breakdown-item.deficit {
    background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
    border-color: var(--error-color);
}

.breakdown-item.required {
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
}

.breakdown-item.available {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    border-color: var(--success-color);
}

.summary-item.primary {
    background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
    border-color: var(--primary-color);
}

.summary-item.info {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    border-color: var(--info-color);
}

.summary-item.secondary {
    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
}

.summary-item.success {
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    border-color: var(--success-color);
}

.item-icon {
    font-size: 1.5rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-md);
    background: rgba(255, 255, 255, 0.8);
}

.item-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.item-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.item-value {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--text-primary);
}

.success-message {
    margin-top: var(--spacing-lg);
    padding: var(--spacing-lg);
    background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
    border-radius: var(--radius-md);
    border: 1px solid var(--success-color);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.message-icon {
    font-size: 1.5rem;
}

.success-message p {
    margin: 0;
    color: var(--text-secondary);
    line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .investment-page-redesign {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .quick-actions {
        grid-column: 1;
        grid-row: 3;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-md);
    }
}

@media (max-width: 768px) {
    .investment-page-redesign {
        padding: var(--spacing-sm);
        gap: var(--spacing-md);
    }

    .smart-header {
        padding: var(--spacing-lg);
        margin-bottom: var(--spacing-md);
    }

    .header-main {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }

    .balance-widget {
        min-width: auto;
        width: 100%;
    }

    .flow-step {
        padding: var(--spacing-lg);
    }

    .step-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: flex-start;
    }

    .plan-preview {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .plan-metrics {
        justify-content: center;
        gap: var(--spacing-md);
    }

    .amount-input-container {
        flex-direction: column;
    }

    .calculate-btn {
        border-top: 1px solid var(--border-color);
        border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    }

    .summary-cards {
        grid-template-columns: 1fr;
    }

    .form-actions {
        flex-direction: column;
    }

    .quick-actions {
        grid-template-columns: 1fr;
    }

    .modal-container {
        max-width: none;
        margin: var(--spacing-md);
    }

    .modal-container.compact {
        margin: var(--spacing-sm);
        max-height: 95vh;
    }

    .modal-header {
        padding: var(--spacing-lg);
    }

    .modal-header.compact {
        padding: var(--spacing-md);
    }

    .modal-body {
        padding: var(--spacing-lg);
    }

    .modal-body.compact {
        padding: 0 var(--spacing-md) var(--spacing-sm) var(--spacing-md);
    }

    .modal-footer {
        padding: var(--spacing-lg);
        flex-direction: column;
    }

    .modal-footer.compact {
        padding: var(--spacing-sm) var(--spacing-md) var(--spacing-md) var(--spacing-md);
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .summary-row {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
}

@media (max-width: 480px) {
    .investment-page-redesign {
        padding: var(--spacing-xs);
    }

    .smart-header {
        padding: var(--spacing-md);
        border-radius: var(--radius-lg);
    }

    .page-title h1 {
        font-size: 1.5rem;
    }

    .balance-value .amount {
        font-size: 1.5rem;
    }

    .flow-step {
        padding: var(--spacing-md);
    }

    .selected-plan-card,
    .investment-form-card {
        padding: var(--spacing-md);
    }

    .plan-icon {
        width: 50px;
        height: 50px;
        font-size: 2rem;
    }

    .metric-value {
        font-size: 1rem;
    }

    .amount-input-container input {
        font-size: 1rem;
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .modal-container {
        margin: var(--spacing-sm);
    }

    .modal-container.compact {
        margin: var(--spacing-xs);
        max-height: 98vh;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--spacing-md);
    }

    .modal-header.compact {
        padding: var(--spacing-sm) var(--spacing-md);
    }

    .modal-header.compact .modal-title h3 {
        font-size: 1rem;
    }

    .modal-header.compact .modal-title p {
        font-size: 0.75rem;
    }

    .modal-body.compact {
        padding: 0 var(--spacing-md) var(--spacing-xs) var(--spacing-md);
    }

    .modal-footer.compact {
        padding: var(--spacing-xs) var(--spacing-md) var(--spacing-md) var(--spacing-md);
    }

    .success-summary.compact {
        padding: var(--spacing-sm);
    }

    .summary-col {
        padding: var(--spacing-xs);
    }

    .summary-label {
        font-size: 0.7rem;
    }

    .summary-value {
        font-size: 0.8rem;
    }

    .primary-action.compact,
    .secondary-action.compact {
        padding: var(--spacing-xs) var(--spacing-sm);
        font-size: 0.8rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #f9fafb;
        --text-secondary: #d1d5db;
        --text-muted: #9ca3af;

        --bg-primary: #1f2937;
        --bg-secondary: #374151;
        --bg-tertiary: #4b5563;

        --border-color: #4b5563;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --border-color: #000000;
        --text-muted: #000000;
    }

    .plan-card,
    .flow-step,
    .modal-content {
        border-width: 3px;
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .loading-spinner,
    .spinner {
        animation: none;
    }

    .status-indicator {
        animation: none;
    }
}

/* ========================================
   INVEST.PHP PAGE SPECIFIC STYLES
   ======================================== */

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
}

.stats-grid .stat-card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.stats-grid .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.stats-grid .stat-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.stats-grid .stat-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.stats-grid .stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.stats-grid .stat-value {
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--text-primary);
}

/* Content Layout */
.content-layout {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: var(--spacing-xl);
    margin-top: var(--spacing-lg);
}

.content-primary {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.content-sidebar {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

/* Content Sections */
.content-section {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.section-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--bg-secondary);
}

.section-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.section-title h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-primary);
}

.section-count {
    font-size: 0.75rem;
    color: var(--text-muted);
    background: var(--bg-tertiary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-weight: 500;
}

.section-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.action-btn-sm {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: 1rem;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn-sm:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.section-content {
    padding: var(--spacing-lg);
}

/* Investments Grid */
.investments-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.investment-item {
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    border: 1px solid var(--border-color);
    transition: all var(--transition-fast);
    cursor: pointer;
}

.investment-item:hover {
    background: var(--bg-primary);
    border-color: var(--primary-color);
    transform: translateX(4px);
    box-shadow: var(--shadow-md);
}

.investment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
}

.investment-amount {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.investment-progress {
    margin-bottom: var(--spacing-md);
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: var(--bg-tertiary);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: var(--spacing-xs);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color), #34d399);
    border-radius: 3px;
    transition: width var(--transition-normal);
}

.progress-text {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.investment-earnings {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
}

.daily-return {
    color: var(--success-color);
    font-weight: 600;
}

.total-earned {
    color: var(--text-secondary);
    font-weight: 500;
}

/* History Grid */
.history-grid {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.history-item {
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    transition: all var(--transition-fast);
}

.history-item:hover {
    background: var(--bg-primary);
    border-color: var(--primary-color);
    transform: translateX(2px);
}

.history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
}

.history-type {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
}

.history-date {
    font-size: 0.75rem;
    color: var(--text-muted);
}

.history-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.history-amount {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--primary-color);
}

.history-status {
    font-size: 0.75rem;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-weight: 500;
    text-transform: uppercase;
}

.history-status.completed {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.history-status.active {
    background: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
}

.history-status.pending {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

/* Sidebar Sections */
.sidebar-section {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.sidebar-section h3 {
    margin: 0;
    padding: var(--spacing-lg);
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Quick Actions */
.quick-actions {
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.quick-action-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    text-decoration: none;
    color: inherit;
    transition: all var(--transition-fast);
}

.quick-action-item:hover {
    background: var(--bg-primary);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
    color: inherit;
    text-decoration: none;
}

.quick-action-item .action-icon {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.quick-action-item .action-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.quick-action-item .action-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
}

.quick-action-item .action-desc {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* Plans Preview */
.plans-preview {
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.plan-preview-item {
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    transition: all var(--transition-fast);
    cursor: pointer;
}

.plan-preview-item:hover {
    background: var(--bg-primary);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.plan-preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
}

.plan-preview-name {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
}

.plan-preview-rate {
    font-size: 0.875rem;
    font-weight: 700;
    color: var(--primary-color);
}

.plan-preview-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* Performance Metrics */
.performance-metrics {
    padding: var(--spacing-md);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.metric-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.metric-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.metric-value {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
}

/* Empty States for Invest Page */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-2xl);
    text-align: center;
    color: var(--text-secondary);
}

.empty-icon {
    font-size: 3rem;
    opacity: 0.6;
}

.empty-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.empty-description {
    font-size: 0.875rem;
    margin: 0;
    max-width: 300px;
    line-height: 1.5;
}

.empty-action {
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    padding: var(--spacing-sm) var(--spacing-lg);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.875rem;
}

.empty-action:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    color: white;
    text-decoration: none;
}

/* Loading States */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-xl);
    color: var(--text-secondary);
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Responsive Styles for Invest Page */
@media (max-width: 1024px) {
    .content-layout {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .content-sidebar {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }

    .content-layout {
        gap: var(--spacing-md);
    }

    .section-header {
        padding: var(--spacing-md);
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: flex-start;
    }

    .section-content {
        padding: var(--spacing-md);
    }

    .investment-item {
        padding: var(--spacing-md);
    }

    .investment-header {
        flex-direction: column;
        gap: var(--spacing-xs);
        align-items: flex-start;
    }

    .quick-actions {
        padding: var(--spacing-sm);
    }

    .quick-action-item {
        padding: var(--spacing-sm);
    }

    .quick-action-item .action-icon {
        font-size: 1.25rem;
    }

    .content-sidebar {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .stats-grid .stat-card {
        padding: var(--spacing-md);
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-sm);
    }

    .section-header {
        padding: var(--spacing-sm);
    }

    .section-content {
        padding: var(--spacing-sm);
    }

    .section-title h2 {
        font-size: 1rem;
    }

    .investment-item,
    .history-item {
        padding: var(--spacing-sm);
    }

    .quick-action-item {
        padding: var(--spacing-xs) var(--spacing-sm);
    }

    .quick-action-item .action-title {
        font-size: 0.75rem;
    }

    .quick-action-item .action-desc {
        font-size: 0.7rem;
    }

    .sidebar-section h3 {
        padding: var(--spacing-md);
        font-size: 0.875rem;
    }

    .performance-metrics,
    .plans-preview {
        padding: var(--spacing-sm);
    }

    .metric-row {
        padding: var(--spacing-xs) var(--spacing-sm);
    }
}
