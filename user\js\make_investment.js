// Make Investment Page JavaScript - SECURITY HARDENED VERSION
let securityUtils = null;
let csrfManager = null;

// Load security utilities first
async function loadSecurityUtils() {
    if (window.SecurityUtils) {
        securityUtils = window.SecurityUtils;
        return;
    }
    
    try {
        await new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'js/security-utils.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
        securityUtils = window.SecurityUtils;
    } catch (error) {
        console.error('Failed to load security utilities:', error);
        // Fallback security function
        securityUtils = {
            escapeHtml: (str) => String(str).replace(/[&<>"']/g, (match) => ({
                '&': '&amp;', '<': '&lt;', '>': '&gt;', '"': '&quot;', "'": '&#39;'
            })[match])
        };
    }
}

document.addEventListener('DOMContentLoaded', async function() {
    await loadSecurityUtils();
    
    // Initialize CSRF manager
    if (typeof CSRFManager !== 'undefined') {
        csrfManager = new CSRFManager();
        csrfManager.setContext('user');
    }
    
    initMakeInvestmentPage();
});

let userBalance = 0;
let selectedPlan = 'basic';
let investmentPlans = {}; // Will be loaded from database

function initMakeInvestmentPage() {
    loadInvestmentPlans();
    loadUserBalance();
    loadActiveInvestments();
    loadInvestmentHistory();
    initFooterMenu();
    initSuccessModalHandlers();
}

function setupInvestmentForm() {
    const investForm = document.getElementById('investmentForm');
    const calculateBtn = document.getElementById('calculateBtn');
    const investAmountInput = document.getElementById('investAmount');
    
    // Set up plan selection after a short delay to ensure DOM is ready
    setTimeout(() => {
        const planCards = document.querySelectorAll('.plan-card:not(.disabled)');
          // Plan selection (only for enabled plans)
        if (planCards.length > 0) {
            planCards.forEach(card => {
                card.addEventListener('click', function() {
                    if (this.classList.contains('disabled')) return;
                    
                    // Remove active class from all cards
                    planCards.forEach(c => c.classList.remove('active'));
                    // Add active class to clicked card
                    this.classList.add('active');
                    
                    selectedPlan = this.dataset.plan;
                    const selectedPlanInput = document.getElementById('selectedPlan');
                    if (selectedPlanInput) {
                        selectedPlanInput.value = selectedPlan;
                    }
                    
                    // Show the investment form
                    showInvestmentForm(selectedPlan);
                    
                    // Update minimum amount
                    if (investmentPlans[selectedPlan]) {
                        const minAmount = investmentPlans[selectedPlan].minAmount;                        if (investAmountInput) {
                            investAmountInput.min = minAmount;
                            investAmountInput.placeholder = `Enter amount (minimum ${securityUtils.escapeHtml(minAmount)} USDT)`;
                        }
                    }
                    
                    // Clear previous calculations
                    const investmentSummary = document.getElementById('investmentSummary');
                    const investBtn = document.getElementById('investBtn');
                    if (investmentSummary) investmentSummary.style.display = 'none';
                    if (investBtn) investBtn.disabled = true;
                });
            });

            // Set default selected plan to basic
            const basicPlanCard = document.querySelector('[data-plan="basic"]:not(.disabled)');
            if (basicPlanCard) {
                basicPlanCard.classList.add('active');
                selectedPlan = 'basic';
            }
        }
    }, 100);

    // Calculate button
    if (calculateBtn) {
        calculateBtn.addEventListener('click', calculateReturns);
    }    // Form submission
    if (investForm) {
        investForm.addEventListener('submit', handleInvestment);
    }    // Amount input validation
    if (investAmountInput) {
        investAmountInput.addEventListener('input', function() {
            const investmentSummary = document.getElementById('investmentSummary');
            const investBtn = document.getElementById('investBtn');
            
            // Hide summary and disable button when user changes amount
            if (investmentSummary) investmentSummary.style.display = 'none';
            if (investBtn) investBtn.disabled = true;
            
            validateAmount();
        });
    }

    // Initialize modal handlers
    initModalHandlers();
}

function showInvestmentForm(planCode) {
    const formCard = document.getElementById('investmentFormCard');
    const plan = investmentPlans[planCode];
    
    if (!formCard || !plan) return;
    
    // Update plan summary in form
    const planNameElement = document.getElementById('selectedPlanName');
    const planRateElement = document.getElementById('selectedPlanRate');
    const planDurationElement = document.getElementById('selectedPlanDuration');
    const planMinimumElement = document.getElementById('selectedPlanMinimum');
    const amountHelpElement = document.getElementById('amountHelp');
      if (planNameElement) planNameElement.textContent = securityUtils.escapeHtml(plan.name);
    if (planRateElement) planRateElement.textContent = `${securityUtils.escapeHtml((plan.dailyRate * 100).toFixed(1))}% Daily`;
    if (planDurationElement) planDurationElement.textContent = `${securityUtils.escapeHtml(plan.duration)} days`;
    if (planMinimumElement) planMinimumElement.textContent = `${securityUtils.escapeHtml(plan.minAmount)} USDT`;
    if (amountHelpElement) amountHelpElement.textContent = `Minimum investment: ${securityUtils.escapeHtml(plan.minAmount)} USDT`;
    
    // Update balance in form
    const formBalanceElement = document.getElementById('formUserBalance');
    if (formBalanceElement) {
        formBalanceElement.textContent = userBalance.toFixed(2);
    }
    
    // Update balance status
    updateBalanceStatus();
    
    // Show the form with animation
    formCard.style.display = 'block';
    
    // Scroll to form
    setTimeout(() => {
        formCard.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }, 100);
}

function updateBalanceStatus() {
    const balanceStatusElement = document.getElementById('balanceStatus');
    const plan = investmentPlans[selectedPlan];
    
    if (!balanceStatusElement || !plan) return;
    
    if (userBalance >= plan.minAmount) {
        balanceStatusElement.textContent = '✅ Sufficient balance for investment';
        balanceStatusElement.className = 'balance-status sufficient';    } else {
        const needed = plan.minAmount - userBalance;
        balanceStatusElement.textContent = `❌ Need ${securityUtils.escapeHtml(needed.toFixed(2))} USDT more`;
        balanceStatusElement.className = 'balance-status insufficient';
    }
}

function validateAmount() {
    const amountInput = document.getElementById('investAmount');
    const validationElement = document.getElementById('amountValidation');
    const plan = investmentPlans[selectedPlan];
    
    if (!amountInput || !validationElement || !plan) return;
    
    const amount = parseFloat(amountInput.value);
    
    if (!amount || isNaN(amount)) {
        validationElement.style.display = 'none';
        return;
    }
    
    if (amount !== plan.minAmount) {
        validationElement.textContent = `Amount should be exactly ${securityUtils.escapeHtml(plan.minAmount)} USDT`;
        validationElement.className = 'amount-validation invalid';
    } else if (amount > userBalance) {
        validationElement.textContent = `Insufficient balance. You need ${securityUtils.escapeHtml((amount - userBalance).toFixed(2))} USDT more.`;
        validationElement.className = 'amount-validation invalid';
    } else {
        validationElement.textContent = `✅ Valid amount. You can proceed with this investment.`;
        validationElement.className = 'amount-validation valid';
    }
}

function initModalHandlers() {
    // Low funds modal handlers
    const closeLowFundsModal = document.getElementById('closeLowFundsModal');
    const addFundsBtn = document.getElementById('addFundsBtn');
    const cancelModalBtn = document.getElementById('cancelModalBtn');
    const lowFundsModal = document.getElementById('lowFundsModal');
    
    if (closeLowFundsModal) {
        closeLowFundsModal.addEventListener('click', hideLowFundsModal);
    }
    
    if (cancelModalBtn) {
        cancelModalBtn.addEventListener('click', hideLowFundsModal);
    }
    
    if (addFundsBtn) {
        addFundsBtn.addEventListener('click', function() {
            window.location.href = 'deposit.php';
        });
    }
    
    // Close modal when clicking outside
    if (lowFundsModal) {
        lowFundsModal.addEventListener('click', function(e) {
            if (e.target === lowFundsModal) {
                hideLowFundsModal();
            }
        });
    }
    
    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            hideLowFundsModal();
        }
    });
}

function showLowFundsModal(requiredAmount) {
    const modal = document.getElementById('lowFundsModal');
    const modalRequiredAmount = document.getElementById('modalRequiredAmount');
    const modalUserBalance = document.getElementById('modalUserBalance');
    const modalDeficitAmount = document.getElementById('modalDeficitAmount');
    
    if (!modal) return;
    
    const deficit = requiredAmount - userBalance;
      if (modalRequiredAmount) modalRequiredAmount.textContent = `${securityUtils.escapeHtml(requiredAmount.toFixed(2))} USDT`;
    if (modalUserBalance) modalUserBalance.textContent = `${securityUtils.escapeHtml(userBalance.toFixed(2))} USDT`;
    if (modalDeficitAmount) modalDeficitAmount.textContent = `${securityUtils.escapeHtml(deficit.toFixed(2))} USDT`;
    
    modal.style.display = 'flex';
    
    // Prevent body scroll
    document.body.style.overflow = 'hidden';
}

function hideLowFundsModal() {
    const modal = document.getElementById('lowFundsModal');
    if (modal) {
        modal.style.display = 'none';
    }
    
    // Restore body scroll
    document.body.style.overflow = '';
}

async function loadUserBalance() {
    try {
        const response = await apiCall('get_balance');
        if (response.success) {
            userBalance = parseFloat(response.balance) || 0;
            const balanceElement = document.getElementById('availableBalance');
            if (balanceElement) {
                balanceElement.textContent = userBalance.toFixed(2);
            }
            
            // Update form balance display if visible
            const formBalanceElement = document.getElementById('formUserBalance');
            if (formBalanceElement) {
                formBalanceElement.textContent = userBalance.toFixed(2);
            }
            
            // Update balance status if form is visible
            if (selectedPlan && investmentPlans[selectedPlan]) {
                updateBalanceStatus();
            }
        } else {
            console.error('Error loading balance:', response.message);
            
            // Handle authentication error specifically
            if (response.error && response.error.includes('authenticated')) {
                userBalance = 0;
                showAuthenticationError();
            } else {
                userBalance = 0;
                const balanceElement = document.getElementById('availableBalance');
                if (balanceElement) balanceElement.textContent = 'Error';
                showMessage('Failed to load balance. Please refresh the page.', 'error');
            }
        }
    } catch (error) {
        console.error('Error loading balance:', error);
        userBalance = 0;
        const balanceElement = document.getElementById('availableBalance');
        if (balanceElement) balanceElement.textContent = 'Error';
        showMessage('Network error loading balance. Please refresh the page.', 'error');
    }
}

function showInsufficientBalanceMessage() {
    const insufficientMsg = document.getElementById('insufficientBalanceMsg');
    const investForm = document.getElementById('investmentForm');
    const planCards = document.querySelector('.investment-plans');
    
    if (insufficientMsg) insufficientMsg.style.display = 'block';
    if (investForm) investForm.style.display = 'none';
    if (planCards) planCards.style.display = 'none';
}

function showAuthenticationError() {
    const balanceElement = document.getElementById('availableBalance');
    const formBalanceElement = document.getElementById('formUserBalance');
    
    if (balanceElement) {
        balanceElement.textContent = 'Please log in';
        balanceElement.style.color = '#dc3545';
    }
    if (formBalanceElement) {
        formBalanceElement.textContent = '0.00';
        formBalanceElement.style.color = '#dc3545';
    }
    
    showMessage('Please log in to view your balance and make investments.', 'error');
}

function calculateReturns() {
    const amountInput = document.getElementById('investAmount');
    if (!amountInput) {
        return;
    }
    
    const amount = parseFloat(amountInput.value);
    
    // Check if selected plan exists
    if (!selectedPlan || !investmentPlans[selectedPlan]) {
        showMessage('Investment plan not available. Please refresh the page.', 'error');
        return;
    }
    
    const plan = investmentPlans[selectedPlan];
    if (!amount || amount < plan.minAmount) {
        showMessage(`Minimum investment for ${securityUtils.escapeHtml(plan.name)} is ${securityUtils.escapeHtml(plan.minAmount)} USDT`, 'error');
        return;
    }
    
    // Special handling for zero balance (likely authentication issue)
    if (userBalance === 0) {
        showMessage('Unable to verify your balance. Please log in again or refresh the page.', 'error');
        return;
    }
    
    if (amount > userBalance) {
        // Show the low funds modal instead of just a message
        showLowFundsModal(amount);
        return;
    }

    const dailyReturn = amount * plan.dailyRate;
    
    const totalReturn = dailyReturn * plan.duration;
    
    // Update summary
    const summaryAmount = document.getElementById('summaryAmount');
    const summaryDailyReturn = document.getElementById('summaryDailyReturn');
    const summaryTotalReturn = document.getElementById('summaryTotalReturn');
    const summaryDuration = document.getElementById('summaryDuration');
      if (summaryAmount) summaryAmount.textContent = `${securityUtils.escapeHtml(amount.toFixed(2))} USDT`;
    if (summaryDailyReturn) summaryDailyReturn.textContent = `${securityUtils.escapeHtml(dailyReturn.toFixed(2))} USDT`;
    if (summaryTotalReturn) summaryTotalReturn.textContent = `${securityUtils.escapeHtml(totalReturn.toFixed(2))} USDT`;
    if (summaryDuration) summaryDuration.textContent = `${securityUtils.escapeHtml(plan.duration)} days`;
    
    const investmentSummary = document.getElementById('investmentSummary');
    const investBtn = document.getElementById('investBtn');
    
    if (investmentSummary) {
        investmentSummary.style.display = 'block';
    }
    if (investBtn) {
        investBtn.disabled = false;
        showMessage('Investment calculation complete! You can now confirm your investment.', 'success');
    } else {
    }
}

async function handleInvestment(e) {
    e.preventDefault();
    
    const investAmountInput = document.getElementById('investAmount');
    if (!investAmountInput) return;
    
    const amount = parseFloat(investAmountInput.value);
      if (!amount || amount < investmentPlans[selectedPlan].minAmount) {
        showMessage(`Minimum investment for ${securityUtils.escapeHtml(investmentPlans[selectedPlan].name)} is ${securityUtils.escapeHtml(investmentPlans[selectedPlan].minAmount)} USDT`, 'error');
        return;
    }
      if (amount > userBalance) {
        // Show the low funds modal instead of just a message
        showLowFundsModal(amount);
        return;
    }
    
    // Disable button to prevent double submission
    const investBtn = document.getElementById('investBtn');
    if (!investBtn) return;
    
    const originalText = investBtn.querySelector('.btn-text')?.textContent || investBtn.textContent;
    investBtn.disabled = true;
    investBtn.classList.add('loading');
    
    try {
        const response = await apiCall('create_investment', {
            amount: amount,
            plan: selectedPlan
        });
          if (response.success) {
            // Show success modal instead of alert
            showSuccessModal(amount, selectedPlan, response.investment_id);
            
            // Reset form
            const investmentForm = document.getElementById('investmentForm');
            const investmentSummary = document.getElementById('investmentSummary');
            const basicPlanCard = document.querySelector('[data-plan="basic"]');
            const otherPlanCards = document.querySelectorAll('.plan-card:not([data-plan="basic"])');
            
            if (investmentForm) investmentForm.reset();
            if (investmentSummary) investmentSummary.style.display = 'none';
            if (basicPlanCard) basicPlanCard.classList.add('active');
            if (otherPlanCards.length > 0) {
                otherPlanCards.forEach(card => {
                    card.classList.remove('active');
                });
            }
            
            // Reload data to update balance and investments
            loadUserBalance();
            loadActiveInvestments();
            loadInvestmentHistory();
            
        } else {
            showMessage(response.message || 'Failed to create investment', 'error');
        }
    } catch (error) {
        console.error('Error creating investment:', error);
        showMessage('Network error. Please try again.', 'error');
    } finally {
        investBtn.disabled = false;
        investBtn.classList.remove('loading');
        if (investBtn.querySelector('.btn-text')) {
            investBtn.querySelector('.btn-text').textContent = originalText;
        } else {
            investBtn.textContent = originalText;
        }
    }
}

async function loadActiveInvestments() {
    try {
        const response = await apiCall('get_active_investments');
        const container = document.getElementById('activeInvestmentsList');
        
        if (!container) return;        if (response.success && response.investments && response.investments.length > 0) {
            container.innerHTML = response.investments.map(investment => `
                <div class="investment-item" data-investment-id="${securityUtils.escapeHtml(investment.id)}" style="cursor: pointer;">
                    <div class="investment-info">
                        <div class="investment-plan">${securityUtils.escapeHtml(investment.plan_name)}</div>
                        <div class="investment-amount">${securityUtils.escapeHtml(parseFloat(investment.amount).toFixed(2))} USDT</div>
                        <div class="investment-progress">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${securityUtils.escapeHtml(Math.round((investment.days_elapsed / 20) * 100))}%"></div>
                            </div>
                            <span class="progress-text">${securityUtils.escapeHtml(investment.days_elapsed)}/20 days</span>
                        </div>                    </div>
                    <div class="investment-earnings">
                        <div class="daily-return">+${securityUtils.escapeHtml(parseFloat(investment.daily_return).toFixed(2))} USDT/day</div>
                        <div class="total-earned">${securityUtils.escapeHtml(parseFloat(investment.total_earned).toFixed(2))} USDT earned</div>
                    </div>
                </div>
            `).join('');
            
            // Add click event listeners to investment items
            container.addEventListener('click', (e) => {
                const investmentItem = e.target.closest('.investment-item');
                if (investmentItem) {
                    const investmentId = investmentItem.dataset.investmentId;
                    if (investmentId) {
                        // Add click animation
                        investmentItem.style.transform = 'scale(0.98)';                        setTimeout(() => {
                            investmentItem.style.transform = '';
                            // Navigate to active plan page with safe parameter encoding
                            const safeId = encodeURIComponent(investmentId);
                            window.location.href = `active_plan.php?id=${safeId}`;
                        }, 150);
                    }
                }
            });
        } else {
            container.innerHTML = '<div class="no-data">No active investments found</div>';
        }
    } catch (error) {
        console.error('Error loading active investments:', error);
        const container = document.getElementById('activeInvestmentsList');
        if (container) {
            container.innerHTML = '<div class="error">Error loading investments</div>';
        }
    }
}

async function loadInvestmentHistory() {
    try {
        const response = await apiCall('get_investment_history');
        const container = document.getElementById('investmentHistoryList');
        
        if (!container) return;
          if (response.success && response.history && response.history.length > 0) {
            container.innerHTML = response.history.map(investment => `
                <div class="history-item">
                    <div class="history-info">
                        <div class="history-plan">${securityUtils.escapeHtml(investment.plan_name)}</div>
                        <div class="history-amount">${securityUtils.escapeHtml(parseFloat(investment.amount).toFixed(2))} USDT</div>
                        <div class="history-date">${securityUtils.escapeHtml(new Date(investment.created_at).toLocaleDateString())}</div>
                    </div>
                    <div class="history-status">
                        <span class="status-badge ${securityUtils.escapeHtml(investment.status)}">${securityUtils.escapeHtml(investment.status)}</span>
                        <div class="history-return">${securityUtils.escapeHtml(parseFloat(investment.total_return).toFixed(2))} USDT</div>
                    </div>
                </div>
            `).join('');
        } else {
            container.innerHTML = '<div class="no-data">No investment history found</div>';
        }
    } catch (error) {
        console.error('Error loading investment history:', error);
        const container = document.getElementById('investmentHistoryList');
        if (container) {
            container.innerHTML = '<div class="error-message">Error loading investment history</div>';
        }
    }
}

async function loadInvestmentPlans() {
    try {
        const response = await apiCall('get_investment_plans');
        
        if (response.success) {
            const plans = response.plans;
            
            // Convert plans array to object for easy access
            investmentPlans = {};
            plans.forEach(plan => {
                investmentPlans[plan.plan_code] = {
                    id: plan.id,
                    name: plan.plan_name,
                    dailyRate: plan.daily_rate,
                    duration: plan.duration,
                    minAmount: plan.min_amount,
                    maxAmount: plan.max_amount,
                    description: plan.description,
                    features: plan.features || [],
                    isActive: plan.is_active,
                    isFeatured: plan.is_featured
                };
            });
            
            renderInvestmentPlans(plans);
            setupInvestmentForm();
        } else {
            showMessage('Failed to load investment plans: ' + (response.message || response.error), 'error');
            renderDefaultPlans();
        }
    } catch (error) {
        console.error('Error loading investment plans:', error);
        showMessage('Failed to load investment plans. Please try again.', 'error');
        renderDefaultPlans();
    }
}

function renderInvestmentPlans(plans) {
    const container = document.getElementById('investmentPlansContainer');
    const planCountElement = document.getElementById('planCount');

    if (!container) return;

    // Update plan count
    if (planCountElement) {
        const activePlans = plans.filter(plan => plan.is_active).length;
        planCountElement.textContent = `${activePlans} plans available`;
    }

    container.innerHTML = '';

    plans.forEach(plan => {
        const isBasic = plan.plan_code === 'basic';
        const isDisabled = !plan.is_active || !isBasic; // Only enable basic plan

        const planCard = document.createElement('div');
        planCard.className = `plan-card ${isDisabled ? 'disabled' : ''}`;
        planCard.dataset.plan = plan.plan_code;

        if (isDisabled) {
            planCard.style.opacity = '0.5';
            planCard.style.pointerEvents = 'none';
        }

        planCard.innerHTML = `
            <div class="plan-header">
                <div class="plan-name">${securityUtils.escapeHtml(plan.plan_name)}</div>
                <div class="plan-rate-container">
                    <div class="plan-rate">${securityUtils.escapeHtml((plan.daily_rate * 100).toFixed(2))}%</div>
                    <div class="plan-period">Daily</div>
                </div>
            </div>
            <div class="plan-details">
                <div class="plan-duration">${securityUtils.escapeHtml(plan.duration)} Days</div>
                <div class="plan-minimum">Min: ${securityUtils.escapeHtml(plan.min_amount)} USDT</div>
            </div>
            ${plan.is_featured ? '<div class="plan-badge">Popular</div>' : ''}
            ${isDisabled && !isBasic ? '<div class="plan-badge coming-soon">Coming Soon</div>' : ''}
            ${isDisabled && !isBasic ? '<div class="plan-overlay"><span>Coming Soon</span></div>' : ''}
        `;

        // Add click event for enabled plans
        if (!isDisabled) {
            planCard.addEventListener('click', () => selectPlan(plan));
        }

        container.appendChild(planCard);
    });
}

// New function to handle plan selection in redesigned interface
function selectPlan(plan) {
    // Remove active class from all plan cards
    document.querySelectorAll('.plan-card').forEach(card => {
        card.classList.remove('active');
    });

    // Add active class to selected plan
    const selectedCard = document.querySelector(`[data-plan="${plan.plan_code}"]`);
    if (selectedCard) {
        selectedCard.classList.add('active');
    }

    // Store selected plan
    selectedPlan = plan;

    // Update hidden input
    const selectedPlanInput = document.getElementById('selectedPlan');
    if (selectedPlanInput) {
        selectedPlanInput.value = plan.plan_code;
    }

    // Update plan summary in step 2
    updatePlanSummary(plan);

    // Show step 2 and hide step 1
    showInvestmentDetails();
}

// New function to update plan summary
function updatePlanSummary(plan) {
    const elements = {
        selectedPlanName: document.getElementById('selectedPlanName'),
        selectedPlanRate: document.getElementById('selectedPlanRate'),
        selectedPlanDuration: document.getElementById('selectedPlanDuration'),
        selectedPlanMinimum: document.getElementById('selectedPlanMinimum')
    };

    if (elements.selectedPlanName) {
        elements.selectedPlanName.textContent = plan.plan_name;
    }
    if (elements.selectedPlanRate) {
        elements.selectedPlanRate.textContent = `${(plan.daily_rate * 100).toFixed(2)}%`;
    }
    if (elements.selectedPlanDuration) {
        elements.selectedPlanDuration.textContent = plan.duration;
    }
    if (elements.selectedPlanMinimum) {
        elements.selectedPlanMinimum.textContent = plan.min_amount;
    }
}

// New function to show investment details step
function showInvestmentDetails() {
    const step1 = document.getElementById('stepPlanSelection');
    const step2 = document.getElementById('stepInvestmentDetails');

    if (step1) {
        step1.style.display = 'none';
        step1.classList.remove('active');
    }

    if (step2) {
        step2.style.display = 'block';
        step2.classList.add('active');

        // Smooth scroll to step 2
        step2.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }

    // Focus on amount input
    const amountInput = document.getElementById('investAmount');
    if (amountInput) {
        setTimeout(() => amountInput.focus(), 300);
    }
}

// New function to go back to plan selection
function goBackToPlans() {
    const step1 = document.getElementById('stepPlanSelection');
    const step2 = document.getElementById('stepInvestmentDetails');

    if (step1) {
        step1.style.display = 'block';
        step1.classList.add('active');
        step1.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }

    if (step2) {
        step2.style.display = 'none';
        step2.classList.remove('active');
    }

    // Clear form
    const form = document.getElementById('investmentForm');
    if (form) {
        form.reset();
    }

    // Hide summary
    const summary = document.getElementById('investmentSummary');
    if (summary) {
        summary.style.display = 'none';
    }

    // Reset validation
    clearValidationMessages();
}

function renderDefaultPlans() {
    // Fallback: render basic plan only if API fails
    const container = document.getElementById('investmentPlansContainer');
    if (!container) return;
    
    investmentPlans = {
        basic: {
            name: 'Basic Plan',
            dailyRate: 0.625,
            duration: 20,
            minAmount: 300,
            features: ['1.25% daily simple interest', 'Non-compounding', '20-day duration', 'Low risk', 'Beginner friendly']
        }
    };
    
    container.innerHTML = `
        <div class="plan-card active" data-plan="basic">
            <div class="plan-header">
                <h4>Basic Plan</h4>
                <div class="plan-badge">Most Popular</div>
            </div>
            <div class="plan-details">
                <div class="plan-return">1.25% Daily Simple Interest</div>
                <div class="plan-duration">20 Days</div>
                <div class="plan-minimum">Min: 300 USDT</div>
            </div>
            <div class="plan-features">
                <ul>
                    <li>✅ 1.25% daily simple interest</li>
                    <li>✅ Non-compounding returns</li>
                    <li>✅ 20-day duration</li>
                    <li>✅ Low risk</li>
                    <li>✅ Beginner friendly</li>
                </ul>
            </div>
        </div>
    `;
    
    setupInvestmentForm();
}

// API call function with CSRF protection
async function apiCall(endpoint, data = null) {
    // Define state-changing operations that require CSRF protection
    const stateChangingActions = [
        'create_investment', 'withdraw', 'record_deposit', 'change_password'
    ];
    
    // Use CSRF manager for state-changing operations
    if (csrfManager && stateChangingActions.includes(endpoint)) {
        try {
            return await csrfManager.secureRequest(endpoint, data);
        } catch (error) {
            console.error('CSRF secure request failed:', error);
            // Fallback to regular request but show error
            showMessage('Security token error. Please refresh the page and try again.', 'error');
            throw error;
        }
    }
    
    // Regular API call for read-only operations
    const url = `../ajax.php`;
    const options = {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: endpoint,
            ...data
        })
    };

    const response = await fetch(url, options);
    return await response.json();
}

function showMessage(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${securityUtils.escapeHtml(type)}`;
    notification.innerHTML = `
        <span>${securityUtils.escapeHtml(message)}</span>
        <button class="notification-close" onclick="this.parentElement.remove()">×</button>
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Initialize footer menu (assuming this function exists globally)
function initFooterMenu() {
    // Footer menu initialization if needed
}

// Success Modal Functions
function showSuccessModal(amount, plan, investmentId) {
    const modal = document.getElementById('successModal');
    const successAmount = document.getElementById('successAmount');
    const successPlan = document.getElementById('successPlan');
    const successInvestmentId = document.getElementById('successInvestmentId');
    const successNewBalance = document.getElementById('successNewBalance');
    
    if (!modal) return;
    
    // Populate modal data
    if (successAmount) successAmount.textContent = `${amount.toFixed(2)} USDT`;
    if (successPlan) successPlan.textContent = investmentPlans[plan]?.name || 'Basic Plan';
    if (successInvestmentId) successInvestmentId.textContent = `#${investmentId}`;
    
    // Calculate and display new balance
    const newBalance = userBalance - amount;
    if (successNewBalance) successNewBalance.textContent = `${newBalance.toFixed(2)} USDT`;
    
    // Update global userBalance for immediate UI updates
    userBalance = newBalance;
    
    // Update balance display in the page
    const balanceElement = document.getElementById('availableBalance');
    if (balanceElement) {
        balanceElement.textContent = newBalance.toFixed(2);
    }
    
    // Show modal
    modal.style.display = 'flex';
    
    // Prevent body scroll
    document.body.style.overflow = 'hidden';
}

function hideSuccessModal() {
    const modal = document.getElementById('successModal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = '';
    }
}

// Initialize Success Modal Event Handlers
function initSuccessModalHandlers() {
    const closeSuccessModal = document.getElementById('closeSuccessModal');
    const makeAnotherInvestmentBtn = document.getElementById('makeAnotherInvestmentBtn');
    const viewDashboardBtn = document.getElementById('viewDashboardBtn');
    const successModal = document.getElementById('successModal');
    
    if (closeSuccessModal) {
        closeSuccessModal.addEventListener('click', hideSuccessModal);
    }
    
    if (makeAnotherInvestmentBtn) {
        makeAnotherInvestmentBtn.addEventListener('click', function() {
            hideSuccessModal();
            // Reload the page to reset form and start fresh
            window.location.reload();
        });
    }
    
    if (viewDashboardBtn) {
        viewDashboardBtn.addEventListener('click', function() {
            window.location.href = 'dashboard.php';
        });
    }
    
    // Close modal when clicking outside
    if (successModal) {
        successModal.addEventListener('click', function(e) {
            if (e.target === successModal) {
                hideSuccessModal();
            }
        });
    }
    
    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            hideSuccessModal();
        }
    });
}

