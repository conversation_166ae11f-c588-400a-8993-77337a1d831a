<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TLS Wallet - New Authentication Demo</title>
    <link rel="stylesheet" href="css/modern-auth.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .demo-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 2rem;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .demo-header {
            text-align: center;
            color: white;
            margin-bottom: 3rem;
        }
        
        .demo-header h1 {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .demo-header p {
            font-size: 1.25rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .demo-links {
            display: flex;
            gap: 2rem;
            flex-wrap: wrap;
            justify-content: center;
            margin-bottom: 3rem;
        }
        
        .demo-link {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 1rem;
            padding: 2rem;
            text-decoration: none;
            color: var(--text-primary);
            transition: all 0.3s ease;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            min-width: 250px;
            text-align: center;
        }
        
        .demo-link:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.2);
        }
        
        .demo-link i {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }
        
        .demo-link h3 {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .demo-link p {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            max-width: 1200px;
            width: 100%;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 1rem;
            padding: 2rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        
        .feature-card h4 {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .feature-card i {
            color: var(--primary-color);
            font-size: 1.5rem;
        }
        
        .feature-card ul {
            list-style: none;
            padding: 0;
        }
        
        .feature-card li {
            padding: 0.5rem 0;
            color: var(--text-secondary);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .feature-card li i {
            color: var(--success-color);
            font-size: 1rem;
        }
        
        @media (max-width: 768px) {
            .demo-header h1 {
                font-size: 2rem;
            }
            
            .demo-links {
                flex-direction: column;
                align-items: center;
            }
            
            .demo-container {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🎉 New TLS Wallet Authentication</h1>
            <p>Experience our completely redesigned login and registration system with modern UI/UX, enhanced security, and mobile-first design.</p>
        </div>
        
        <div class="demo-links">
            <a href="login.php" class="demo-link">
                <i class="fas fa-sign-in-alt"></i>
                <h3>New Login Page</h3>
                <p>Modern, secure, and user-friendly login experience</p>
            </a>
            
            <a href="register.php" class="demo-link">
                <i class="fas fa-user-plus"></i>
                <h3>New Registration</h3>
                <p>Enhanced registration with live password validation</p>
            </a>
        </div>
        
        <div class="features-grid">
            <div class="feature-card">
                <h4><i class="fas fa-mobile-alt"></i> Mobile-First Design</h4>
                <ul>
                    <li><i class="fas fa-check"></i> Responsive layout for all devices</li>
                    <li><i class="fas fa-check"></i> Touch-friendly interface</li>
                    <li><i class="fas fa-check"></i> Optimized for mobile performance</li>
                    <li><i class="fas fa-check"></i> Progressive Web App ready</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h4><i class="fas fa-shield-alt"></i> Enhanced Security</h4>
                <ul>
                    <li><i class="fas fa-check"></i> Real-time password strength validation</li>
                    <li><i class="fas fa-check"></i> Live password confirmation</li>
                    <li><i class="fas fa-check"></i> Secure PIN validation</li>
                    <li><i class="fas fa-check"></i> CSRF protection</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h4><i class="fas fa-palette"></i> Modern UI/UX</h4>
                <ul>
                    <li><i class="fas fa-check"></i> Glassmorphism design effects</li>
                    <li><i class="fas fa-check"></i> Smooth animations and transitions</li>
                    <li><i class="fas fa-check"></i> Interactive form elements</li>
                    <li><i class="fas fa-check"></i> Accessibility compliant</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h4><i class="fas fa-bolt"></i> Live Validation</h4>
                <ul>
                    <li><i class="fas fa-check"></i> Real-time password strength meter</li>
                    <li><i class="fas fa-check"></i> Instant password match verification</li>
                    <li><i class="fas fa-check"></i> Email format validation</li>
                    <li><i class="fas fa-check"></i> PIN format checking</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h4><i class="fas fa-eye"></i> User Experience</h4>
                <ul>
                    <li><i class="fas fa-check"></i> Password visibility toggle</li>
                    <li><i class="fas fa-check"></i> Loading states and feedback</li>
                    <li><i class="fas fa-check"></i> Clear error messages</li>
                    <li><i class="fas fa-check"></i> Intuitive form flow</li>
                </ul>
            </div>
            
            <div class="feature-card">
                <h4><i class="fas fa-universal-access"></i> Accessibility</h4>
                <ul>
                    <li><i class="fas fa-check"></i> Screen reader compatible</li>
                    <li><i class="fas fa-check"></i> Keyboard navigation support</li>
                    <li><i class="fas fa-check"></i> High contrast mode support</li>
                    <li><i class="fas fa-check"></i> Reduced motion preferences</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
