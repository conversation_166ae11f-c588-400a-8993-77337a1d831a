<?php
require_once __DIR__ . '/autoload.php';

use Frontend\Config\FrontendConfig;
use Frontend\Services\SessionService;

// Initialize configuration and session
FrontendConfig::init();
SessionService::init();

// Redirect if already logged in
if (SessionService::isAuthenticated()) {
    if (SessionService::isAdmin()) {
        header('Location: admin.php');
    } else {
        header('Location: user/dashboard.php');
    }
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - TLS Wallet</title>
    <link rel="stylesheet" href="css/modern-auth.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <div class="auth-wrapper">
        <!-- Background Elements -->
        <div class="bg-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
        </div>
        
        <!-- Main Content -->
        <div class="auth-container">
            <!-- Header Section -->
            <div class="auth-header">
                <div class="logo-container">
                    <div class="logo-icon">
                        <i class="fas fa-wallet"></i>
                    </div>
                    <h1>TLS Wallet</h1>
                </div>
                <p class="subtitle">Secure TRON & USDT Management</p>
            </div>

            <!-- Login Form -->
            <div class="auth-card">
                <div class="card-header">
                    <h2>Welcome Back</h2>
                    <p>Sign in to your account to continue</p>
                </div>

                <form id="loginForm" class="auth-form">
                    <div class="form-group">
                        <label for="email">Email Address</label>
                        <div class="input-wrapper">
                            <i class="fas fa-envelope input-icon"></i>
                            <input type="email" id="email" name="email" required autocomplete="email">
                            <div class="input-focus-border"></div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="password">Password</label>
                        <div class="input-wrapper">
                            <i class="fas fa-lock input-icon"></i>
                            <input type="password" id="password" name="password" required autocomplete="current-password">
                            <button type="button" class="password-toggle" onclick="togglePassword('password')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <div class="input-focus-border"></div>
                        </div>
                    </div>

                    <div class="form-options">
                        <label class="checkbox-container">
                            <input type="checkbox" id="remember" name="remember">
                            <span class="checkmark"></span>
                            Remember me
                        </label>
                        <a href="#" class="forgot-link" onclick="showForgotPassword()">Forgot Password?</a>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <span class="btn-text">Sign In</span>
                        <div class="btn-loader">
                            <div class="spinner"></div>
                        </div>
                    </button>
                </form>

                <div class="auth-footer">
                    <p>Don't have an account? <a href="register.php" class="register-link">Create Account</a></p>
                </div>
            </div>

            <!-- Forgot Password Modal -->
            <div id="forgotPasswordModal" class="modal">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>Reset Password</h3>
                        <button type="button" class="modal-close" onclick="closeForgotPassword()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <form id="forgotPasswordForm">
                        <div class="form-group">
                            <label for="forgotEmail">Email Address</label>
                            <div class="input-wrapper">
                                <i class="fas fa-envelope input-icon"></i>
                                <input type="email" id="forgotEmail" name="email" required>
                                <div class="input-focus-border"></div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">
                            <span class="btn-text">Send Reset Link</span>
                            <div class="btn-loader">
                                <div class="spinner"></div>
                            </div>
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Message Container -->
        <div id="message" class="message-container"></div>
    </div>

    <script src="user/js/csrf-manager.js"></script>
    <script src="user/js/modern-auth.js"></script>
</body>
</html>
