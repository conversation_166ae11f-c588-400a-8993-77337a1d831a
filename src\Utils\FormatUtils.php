<?php

declare(strict_types=1);

namespace Frontend\Utils;

/**
 * PSR-4 compliant formatting utility
 * Provides formatting methods for display
 */
class FormatUtils
{
    /**
     * Format currency amount
     */
    public static function formatCurrency(float $amount, string $currency = 'TRX', int $decimals = 6): string
    {
        return number_format($amount, $decimals) . ' ' . $currency;
    }

    /**
     * Format percentage
     */
    public static function formatPercentage(float $value, int $decimals = 2): string
    {
        return number_format($value * 100, $decimals) . '%';
    }

    /**
     * Format date
     */
    public static function formatDate(string $date, string $format = 'Y-m-d H:i:s'): string
    {
        $timestamp = is_numeric($date) ? (int)$date : strtotime($date);
        return date($format, $timestamp);
    }

    /**
     * Format date for human reading
     */
    public static function formatDateHuman(string $date): string
    {
        $timestamp = is_numeric($date) ? (int)$date : strtotime($date);
        $diff = time() - $timestamp;
        
        if ($diff < 60) {
            return 'Just now';
        } elseif ($diff < 3600) {
            $minutes = floor($diff / 60);
            return $minutes . ' minute' . ($minutes > 1 ? 's' : '') . ' ago';
        } elseif ($diff < 86400) {
            $hours = floor($diff / 3600);
            return $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ago';
        } elseif ($diff < 604800) {
            $days = floor($diff / 86400);
            return $days . ' day' . ($days > 1 ? 's' : '') . ' ago';
        } else {
            return date('M j, Y', $timestamp);
        }
    }

    /**
     * Format file size
     */
    public static function formatFileSize(int $size): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $unitIndex = 0;
        
        while ($size >= 1024 && $unitIndex < count($units) - 1) {
            $size /= 1024;
            $unitIndex++;
        }
        
        return round($size, 2) . ' ' . $units[$unitIndex];
    }

    /**
     * Format transaction hash (truncated)
     */
    public static function formatTransactionHash(string $hash, int $startChars = 8, int $endChars = 8): string
    {
        if (strlen($hash) <= ($startChars + $endChars + 3)) {
            return $hash;
        }
        
        return substr($hash, 0, $startChars) . '...' . substr($hash, -$endChars);
    }

    /**
     * Format wallet address (truncated)
     */
    public static function formatWalletAddress(string $address, int $startChars = 6, int $endChars = 6): string
    {
        if (strlen($address) <= ($startChars + $endChars + 3)) {
            return $address;
        }
        
        return substr($address, 0, $startChars) . '...' . substr($address, -$endChars);
    }

    /**
     * Format number with commas
     */
    public static function formatNumber(float $number, int $decimals = 0): string
    {
        return number_format($number, $decimals);
    }

    /**
     * Format duration
     */
    public static function formatDuration(int $seconds): string
    {
        if ($seconds < 60) {
            return $seconds . 's';
        } elseif ($seconds < 3600) {
            $minutes = floor($seconds / 60);
            $remainingSeconds = $seconds % 60;
            return $minutes . 'm' . ($remainingSeconds > 0 ? ' ' . $remainingSeconds . 's' : '');
        } elseif ($seconds < 86400) {
            $hours = floor($seconds / 3600);
            $remainingMinutes = floor(($seconds % 3600) / 60);
            return $hours . 'h' . ($remainingMinutes > 0 ? ' ' . $remainingMinutes . 'm' : '');
        } else {
            $days = floor($seconds / 86400);
            $remainingHours = floor(($seconds % 86400) / 3600);
            return $days . 'd' . ($remainingHours > 0 ? ' ' . $remainingHours . 'h' : '');
        }
    }

    /**
     * Format status with color class
     */
    public static function formatStatus(string $status): array
    {
        $statusMap = [
            'active' => ['text' => 'Active', 'class' => 'success'],
            'pending' => ['text' => 'Pending', 'class' => 'warning'],
            'completed' => ['text' => 'Completed', 'class' => 'success'],
            'failed' => ['text' => 'Failed', 'class' => 'danger'],
            'cancelled' => ['text' => 'Cancelled', 'class' => 'secondary'],
            'processing' => ['text' => 'Processing', 'class' => 'info'],
            'confirmed' => ['text' => 'Confirmed', 'class' => 'success'],
            'expired' => ['text' => 'Expired', 'class' => 'danger']
        ];
        
        return $statusMap[strtolower($status)] ?? [
            'text' => ucfirst($status),
            'class' => 'secondary'
        ];
    }

    /**
     * Format investment plan name
     */
    public static function formatPlanName(string $plan): string
    {
        $planNames = [
            'basic' => 'Basic Plan',
            'premium' => 'Premium Plan',
            'ultimate' => 'Ultimate Plan'
        ];
        
        return $planNames[strtolower($plan)] ?? ucfirst($plan) . ' Plan';
    }

    /**
     * Format phone number
     */
    public static function formatPhoneNumber(string $phone): string
    {
        $cleaned = preg_replace('/[^0-9]/', '', $phone);
        
        if (strlen($cleaned) === 10) {
            return preg_replace('/(\d{3})(\d{3})(\d{4})/', '($1) $2-$3', $cleaned);
        } elseif (strlen($cleaned) === 11 && $cleaned[0] === '1') {
            return preg_replace('/(\d{1})(\d{3})(\d{3})(\d{4})/', '+$1 ($2) $3-$4', $cleaned);
        }
        
        return $phone;
    }

    /**
     * Format text for display (truncate with ellipsis)
     */
    public static function truncateText(string $text, int $length = 100, string $suffix = '...'): string
    {
        if (strlen($text) <= $length) {
            return $text;
        }
        
        return rtrim(substr($text, 0, $length)) . $suffix;
    }

    /**
     * Format array as readable list
     */
    public static function formatList(array $items, string $separator = ', ', string $lastSeparator = ' and '): string
    {
        if (empty($items)) {
            return '';
        }
        
        if (count($items) === 1) {
            return $items[0];
        }
        
        if (count($items) === 2) {
            return implode($lastSeparator, $items);
        }
        
        $lastItem = array_pop($items);
        return implode($separator, $items) . $lastSeparator . $lastItem;
    }

    /**
     * Format boolean as Yes/No
     */
    public static function formatBoolean(bool $value, string $trueText = 'Yes', string $falseText = 'No'): string
    {
        return $value ? $trueText : $falseText;
    }

    /**
     * Format JSON for display
     */
    public static function formatJson(string $json, bool $prettyPrint = true): string
    {
        $data = json_decode($json, true);
        
        if (json_last_error() !== JSON_ERROR_NONE) {
            return $json;
        }
        
        return $prettyPrint ? json_encode($data, JSON_PRETTY_PRINT) : json_encode($data);
    }

    /**
     * Format error message
     */
    public static function formatError(string $message): string
    {
        return '<div class="alert alert-danger">' . htmlspecialchars($message) . '</div>';
    }

    /**
     * Format success message
     */
    public static function formatSuccess(string $message): string
    {
        return '<div class="alert alert-success">' . htmlspecialchars($message) . '</div>';
    }

    /**
     * Format warning message
     */
    public static function formatWarning(string $message): string
    {
        return '<div class="alert alert-warning">' . htmlspecialchars($message) . '</div>';
    }

    /**
     * Format info message
     */
    public static function formatInfo(string $message): string
    {
        return '<div class="alert alert-info">' . htmlspecialchars($message) . '</div>';
    }

    /**
     * Format money with proper precision
     */
    public static function formatMoney(float $amount, int $precision = 2): string
    {
        return '$' . number_format($amount, $precision);
    }

    /**
     * Generate CSS class for amount (positive/negative)
     */
    public static function getAmountClass(float $amount): string
    {
        if ($amount > 0) {
            return 'text-success';
        } elseif ($amount < 0) {
            return 'text-danger';
        } else {
            return 'text-muted';
        }
    }

    /**
     * Format user role
     */
    public static function formatUserRole(bool $isAdmin): string
    {
        return $isAdmin ? 'Administrator' : 'User';
    }

    /**
     * Format badge HTML
     */
    public static function formatBadge(string $text, string $type = 'primary'): string
    {
        return '<span class="badge badge-' . htmlspecialchars($type) . '">' . htmlspecialchars($text) . '</span>';
    }
}
