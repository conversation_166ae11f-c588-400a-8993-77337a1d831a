<?php
// Include PSR-4 autoloader
require_once __DIR__ . '/../autoload.php';

use Frontend\Config\FrontendConfig;
use Frontend\Services\SessionService;

// Initialize PSR-4 configuration and session
FrontendConfig::init();
SessionService::init();

// Check if user is logged in
if (!SessionService::isAuthenticated()) {
    header('Location: ../index.php');
    exit;
}

$user = SessionService::getCurrentUser();

// Set variables for header
$pageTitle = 'Transaction History - TLS Wallet';
$currentPage = 'transactions';
$basePath = '.';
$cssPath = 'css';

// Include header
include '../includes/header.php';
?>

            <!-- Transactions Content -->
            <div class="transactions-page">                <div class="page-header">
                    <h2>Transaction History</h2>
                    <p>View and manage your transaction history</p>
                </div>

                <!-- Transaction List -->
                <div class="card">
                    <h3>Transactions</h3>
                    <div id="transactionsList">Loading...</div>
                    
                    <!-- Pagination -->
                    <div class="pagination-container">
                        <div class="pagination-info">
                            <span id="paginationInfo">Showing 0 of 0 transactions</span>
                        </div>
                        <div class="pagination-controls">
                            <button id="prevPageBtn" class="btn btn-outline btn-sm" disabled>Previous</button>
                            <span id="pageInfo">Page 1 of 1</span>
                            <button id="nextPageBtn" class="btn btn-outline btn-sm" disabled>Next</button>
                        </div>
                    </div>
                </div>
            </div>

<?php 
// Include footer
include '../includes/footer.php';
?>    

<script src="js/qr-generator.js"></script>
    <script src="js/transactions.js"></script>
</body>
</html>
