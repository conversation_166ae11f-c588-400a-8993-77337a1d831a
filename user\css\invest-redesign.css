/* ========================================
   MODERN INVESTMENT DASHBOARD REDESIGN
   ======================================== */

/* CSS Variables for Consistent Theming */
:root {
    --primary-color: #667eea;
    --primary-dark: #4f46e5;
    --primary-light: #818cf8;
    --secondary-color: #f59e0b;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;
    
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-muted: #9ca3af;
    
    --bg-primary: #ffffff;
    --bg-secondary: #f9fafb;
    --bg-tertiary: #f3f4f6;
    
    --border-color: #e5e7eb;
    --border-focus: #667eea;
    
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
}

/* Investment Dashboard Layout */
.investment-dashboard {
    padding: var(--spacing-md);
    max-width: 1400px;
    margin: 0 auto;
    background: var(--bg-secondary);
    min-height: calc(100vh - 140px);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

/* Dashboard Header */
.dashboard-header {
    grid-column: 1 / -1;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    color: white;
    box-shadow: var(--shadow-lg);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--spacing-lg);
    width: 100%;
    max-width: 100%;
    overflow: hidden;
}

.page-title {
    flex: 1;
    min-width: 0;
}

.page-title h1 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0 0 var(--spacing-xs) 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.page-title p {
    margin: 0;
    opacity: 0.9;
    font-size: 1.1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.header-actions {
    display: flex;
    gap: var(--spacing-md);
    flex-shrink: 0;
    align-items: center;
}

.action-btn {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md) var(--spacing-lg);
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.875rem;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-1px);
}

.action-btn.primary {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
}

.action-btn.secondary {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}

/* Dashboard Stats */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.stat-card {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.stat-card:hover::before {
    transform: scaleX(1);
}

.stat-card.balance::before {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.stat-card.investments::before {
    background: linear-gradient(90deg, var(--success-color), #34d399);
}

.stat-card.earnings::before {
    background: linear-gradient(90deg, var(--warning-color), #fbbf24);
}

.stat-card.daily::before {
    background: linear-gradient(90deg, var(--info-color), #60a5fa);
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-md);
    display: block;
}

.stat-content {
    margin-bottom: var(--spacing-md);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
    margin-bottom: var(--spacing-xs);
    display: block;
}

.stat-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    line-height: 1.2;
    display: block;
}

.stat-currency {
    font-size: 0.875rem;
    color: var(--text-muted);
    font-weight: 500;
}

.stat-status, .stat-trend {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.875rem;
}

.stat-status {
    color: var(--text-secondary);
}

.stat-trend {
    color: var(--success-color);
}

.stat-trend.positive {
    color: var(--success-color);
}

.stat-trend.negative {
    color: var(--error-color);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.trend-icon {
    font-size: 1rem;
}

/* Dashboard Content Layout */
.dashboard-content {
    display: grid;
    grid-template-columns: 1fr 320px;
    gap: var(--spacing-xl);
}

.content-main {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.content-sidebar {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

/* Dashboard Widgets */
.dashboard-widget {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.dashboard-widget:hover {
    box-shadow: var(--shadow-lg);
}

.dashboard-widget.compact {
    border-radius: var(--radius-lg);
}

.widget-header {
    padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-md) var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dashboard-widget.compact .widget-header {
    padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md) var(--spacing-lg);
}

.widget-title {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.widget-title h3 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.dashboard-widget.compact .widget-title h3 {
    font-size: 1rem;
}

.widget-count {
    font-size: 0.75rem;
    color: var(--text-muted);
    background: var(--bg-tertiary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-md);
    font-weight: 500;
}

.widget-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.widget-btn {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
}

.widget-btn:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
}

.widget-content {
    padding: var(--spacing-xl);
}

.dashboard-widget.compact .widget-content {
    padding: var(--spacing-lg);
}

/* Loading State */
.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .investment-dashboard {
        padding: var(--spacing-sm);
        gap: var(--spacing-md);
    }

    .dashboard-header {
        padding: var(--spacing-lg);
        border-radius: var(--radius-lg);
    }

    .header-content {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }

    .page-title h1 {
        font-size: 1.5rem;
        justify-content: center;
    }

    .page-title p {
        font-size: 1rem;
    }

    .header-actions {
        width: 100%;
        justify-content: center;
        flex-wrap: wrap;
    }

    .action-btn {
        flex: 1;
        min-width: 140px;
        justify-content: center;
    }

    .dashboard-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }

    .dashboard-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .investment-dashboard {
        padding: var(--spacing-xs);
    }

    .dashboard-header {
        padding: var(--spacing-md);
    }

    .page-title h1 {
        font-size: 1.25rem;
    }

    .page-title p {
        font-size: 0.875rem;
    }

    .action-btn {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: 0.75rem;
        min-width: 120px;
    }

    .dashboard-stats {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
    }

    .header-actions {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .action-btn {
        width: 100%;
        flex: none;
    }
}
