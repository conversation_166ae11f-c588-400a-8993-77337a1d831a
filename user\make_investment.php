<?php
// Include PSR-4 autoloader
require_once __DIR__ . '/../autoload.php';

use Frontend\Config\FrontendConfig;
use Frontend\Services\SessionService;

// Initialize PSR-4 configuration and session
FrontendConfig::init();
SessionService::init();

// Check if user is logged in
if (!SessionService::isAuthenticated()) {
    header('Location: ../index.php');
    exit;
}

$user = SessionService::getCurrentUser();

// Set variables for header
$pageTitle = 'Make Investment - TLS Wallet';
$currentPage = 'make_investment';
$basePath = '.';
$cssPath = '../css';

// Include header
include '../includes/header.php';
?>

            <!-- Make Investment Content -->
            <div class="make-investment-page compact">
                <!-- Compact Header with Balance -->
                <div class="compact-header">
                    <div class="header-info">
                        <h2>Make Investment</h2>
                        <p>Choose plan and start earning</p>
                    </div>
                    <div class="balance-display">
                        <div class="balance-label">Available</div>
                        <div class="balance-amount" id="availableBalance">Loading...</div>
                        <div class="balance-currency">USDT</div>
                    </div>
                </div>

                <!-- Compact Investment Plans -->
                <div class="investment-plans-section compact">
                    <div class="section-header">
                        <h3>Investment Plans</h3>
                        <div class="plan-count" id="planCount">Loading...</div>
                    </div>
                    <div class="investment-plans compact-grid" id="investmentPlansContainer">
                        <!-- Plans will be loaded dynamically from database -->
                        <div class="loading-spinner">Loading plans...</div>
                    </div>
                </div><!-- Compact Investment Form -->
                <div class="compact-card" id="investmentFormCard" style="display: none;">
                    <div class="form-header">
                        <h3>Investment Details</h3>
                        <div class="form-balance">
                            <span class="balance-label">Balance:</span>
                            <span id="formUserBalance">0.00</span> USDT
                            <div class="balance-status" id="balanceStatus"></div>
                        </div>
                    </div>

                    <div class="investment-form-container compact">
                        <form id="investmentForm" class="investment-form compact">
                            <input type="hidden" id="selectedPlan" name="selectedPlan" value="basic">

                            <!-- Compact Plan Summary -->
                            <div class="selected-plan-summary compact">
                                <div class="plan-summary-row">
                                    <div class="plan-info">
                                        <span class="plan-name" id="selectedPlanName">Basic Plan</span>
                                        <span class="plan-rate" id="selectedPlanRate">5.0% Daily</span>
                                    </div>
                                    <div class="plan-details">
                                        <span class="plan-duration" id="selectedPlanDuration">20 days</span>
                                        <span class="plan-minimum" id="selectedPlanMinimum">Min: 300 USDT</span>
                                    </div>
                                </div>
                            </div>
                            <!-- Compact Form Row -->
                            <div class="form-row compact">
                                <div class="form-group amount-input">
                                    <label for="investAmount">Amount (USDT)</label>
                                    <input type="number" id="investAmount" name="investAmount"
                                           min="300" max="300" step="0.01" placeholder="300" required>
                                    <div class="input-help" id="amountHelp">Must be exactly 300 USDT</div>
                                    <div class="amount-validation" id="amountValidation"></div>
                                </div>
                                <div class="form-group calculate-group">
                                    <label>&nbsp;</label>
                                    <button type="button" id="calculateBtn" class="btn btn-outline compact">Calculate</button>
                                </div>
                            </div>

                            <!-- Compact Investment Summary -->
                            <div class="investment-summary compact" id="investmentSummary" style="display: none;">
                                <div class="summary-header">
                                    <h4>Summary</h4>
                                </div>
                                <div class="summary-grid">
                                    <div class="summary-item">
                                        <span class="summary-label">Amount</span>
                                        <span class="summary-value" id="summaryAmount">0 USDT</span>
                                    </div>
                                    <div class="summary-item">
                                        <span class="summary-label">Daily</span>
                                        <span class="summary-value" id="summaryDailyReturn">0 USDT</span>
                                    </div>
                                    <div class="summary-item">
                                        <span class="summary-label">Total</span>
                                        <span class="summary-value" id="summaryTotalReturn">0 USDT</span>
                                    </div>
                                    <div class="summary-item">
                                        <span class="summary-label">Duration</span>
                                        <span class="summary-value" id="summaryDuration">0 days</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Compact Form Actions -->
                            <div class="form-actions compact">
                                <button type="submit" id="investBtn" class="btn btn-primary compact" disabled>Confirm Investment</button>
                                <button type="button" id="backBtn" class="btn btn-secondary compact" onclick="window.location.href='invest.php'">Back</button>
                            </div>
                        </form>

                        <!-- Compact Insufficient Balance Message -->
                        <div id="insufficientBalanceMsg" class="insufficient-balance compact" style="display: none;">
                            <div class="warning-card compact">
                                <div class="warning-content">
                                    <div class="warning-icon">⚠️</div>
                                    <div class="warning-text">
                                        <h4>Insufficient Balance</h4>
                                        <p>Add funds to continue investing</p>
                                    </div>
                                </div>
                                <div class="warning-actions compact">
                                    <a href="deposit.php" class="btn btn-primary compact">Add Funds</a>
                                    <button type="button" class="btn btn-secondary compact" onclick="window.location.href='invest.php'">Back</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Compact Low Funds Modal -->
            <div id="lowFundsModal" class="modal compact" style="display: none;">
                <div class="modal-content compact">
                    <div class="modal-header compact">
                        <div class="modal-title">
                            <span class="modal-icon">⚠️</span>
                            <h3>Insufficient Balance</h3>
                        </div>
                        <button type="button" class="modal-close" id="closeLowFundsModal">&times;</button>
                    </div>
                    <div class="modal-body compact">
                        <div class="balance-comparison compact">
                            <div class="comparison-item">
                                <span class="comparison-label">Required:</span>
                                <span class="comparison-value required" id="modalRequiredAmount">0 USDT</span>
                            </div>
                            <div class="comparison-item">
                                <span class="comparison-label">Available:</span>
                                <span class="comparison-value available" id="modalUserBalance">0 USDT</span>
                            </div>
                            <div class="comparison-item deficit">
                                <span class="comparison-label">Needed:</span>
                                <span class="comparison-value deficit" id="modalDeficitAmount">0 USDT</span>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer compact">
                        <button type="button" class="btn btn-primary compact" id="addFundsBtn">
                            <span class="btn-icon">+</span>
                            Add Funds
                        </button>
                        <button type="button" class="btn btn-secondary compact" id="cancelModalBtn">Cancel</button>
                    </div>
                </div>
            </div>

            <!-- Compact Success Modal -->
            <div id="successModal" class="modal compact" style="display: none;">
                <div class="modal-content success-modal compact">
                    <div class="modal-header success-header compact">
                        <div class="success-title">
                            <span class="success-icon">🎉</span>
                            <h3>Investment Created!</h3>
                        </div>
                        <button type="button" class="modal-close" id="closeSuccessModal">&times;</button>
                    </div>
                    <div class="modal-body compact">
                        <div class="success-details">
                            <div class="detail-grid">
                                <div class="detail-item">
                                    <span class="detail-label">Amount</span>
                                    <span class="detail-value" id="successAmount">0 USDT</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Plan</span>
                                    <span class="detail-value" id="successPlan">Basic Plan</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">ID</span>
                                    <span class="detail-value" id="successInvestmentId">#12345</span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Balance</span>
                                    <span class="detail-value" id="successNewBalance">0 USDT</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer compact">
                        <button type="button" class="btn btn-primary compact" id="makeAnotherInvestmentBtn">
                            <span class="btn-icon">+</span>
                            Invest Again
                        </button>
                        <button type="button" class="btn btn-secondary compact" id="viewDashboardBtn">Dashboard</button>
                    </div>
                </div>
            </div>

<?php 
// Include footer
include '../includes/footer.php';
?>
    <script src="js/csrf-manager.js"></script>
    <script src="js/make_investment.js"></script>
</body>
</html>
