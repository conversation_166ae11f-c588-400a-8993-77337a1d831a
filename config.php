<?php
/**
 * Frontend Configuration
 * 
 * Simple configuration for the TLS Crypto Wallet Frontend
 * This file contains basic settings for the standalone frontend
 */

// Basic application settings
define('APP_NAME', 'TLS Crypto Wallet');
define('APP_VERSION', '1.0.0');

// API Configuration
define('API_BASE_URL', 'http://localhost:8000/');
define('API_TIMEOUT', 30);

// Session Configuration
define('SESSION_LIFETIME', 3600);
define('SESSION_NAME', 'tls_session');

// Security Settings
define('ENABLE_HTTPS_ONLY', false);
define('SECURE_COOKIES', false);

// Application Defaults
define('DEFAULT_CURRENCY', 'TRX');
define('DEFAULT_PAGE_SIZE', 20);
define('MAX_PAGE_SIZE', 100);
define('MAX_UPLOAD_SIZE', 5242880); // 5MB

// Transaction Settings
define('MIN_WITHDRAWAL_AMOUNT', 375.00);
define('PROCESSING_FEE_PERCENT', 10);

// File Upload Settings
define('ALLOWED_UPLOAD_TYPES', serialize(['jpg', 'jpeg', 'png', 'gif']));

// Database Configuration (if needed for local storage)
define('DB_HOST', 'localhost');
define('DB_NAME', 'tls_frontend');
define('DB_USER', 'root');
define('DB_PASS', '');

// Error Reporting
define('SHOW_ERRORS', false);
define('LOG_ERRORS', true);

// Timezone
date_default_timezone_set('UTC');