<?php
// Reusable header component for user dashboard pages
// Requires: $pageTitle to be set before including this file
// Optional: $currentPage to set active navigation

if (!isset($pageTitle)) {
    $pageTitle = 'TRON Wallet';
}

$currentPage = $currentPage ?? '';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="color-scheme" content="light only">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($pageTitle); ?></title>
    <link rel="stylesheet" href="<?php echo isset($cssPath) ? $cssPath : 'css'; ?>/style.css">
    <link rel="stylesheet" href="<?php echo isset($cssPath) ? $cssPath : 'css'; ?>/dashboard.css">
    <?php if (isset($additionalCSS)): ?>
        <?php foreach ($additionalCSS as $css): ?>
            <link rel="stylesheet" href="<?php echo htmlspecialchars($css); ?>">
        <?php endforeach; ?>
    <?php endif; ?>
    <link rel="manifest" href="/manifest.webmanifest">
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
          navigator.serviceWorker.register('/service-worker.js').then(function(registration) {
            // Registration successful
          }, function(err) {
            // Registration failed
            console.warn('ServiceWorker registration failed:', err);
          });
        });
      }
    </script>
</head>
<body>
    <div class="app-container">
        <!-- Mobile-First Header -->
        <header class="app-header">
            <div class="header-content">
                <div class="header-brand">
                    <h1>TLS Wallet</h1>
                    <span class="brand-subtitle">TRON · USDT</span>
                </div>
                <div class="header-actions">
                    <button class="btn btn-outline btn-sm header-btn" onclick="window.location.href='<?php echo isset($basePath) ? $basePath : '.'; ?>/profile.php'">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                            <circle cx="12" cy="7" r="4"/>
                        </svg>
                        <span class="btn-text">Profile</span>
                    </button>
                    <button class="btn btn-danger btn-sm header-btn" onclick="window.location.href='../index.php?logout=1'">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
                            <polyline points="16,17 21,12 16,7"/>
                            <line x1="21" y1="12" x2="9" y2="12"/>
                        </svg>
                        <span class="btn-text">Logout</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="app-main">
