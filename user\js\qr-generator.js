// QR Code Generator for Deposit Addresses
class QRCodeGenerator {
    constructor() {
        this.qrApiUrl = 'https://api.qrserver.com/v1/create-qr-code/';
    }

    generateQRCode(text, size = 200) {
        const params = new URLSearchParams({
            size: `${size}x${size}`,
            data: text,
            format: 'png',
            bgcolor: 'ffffff',
            color: '000000',
            margin: 10
        });

        return `${this.qrApiUrl}?${params.toString()}`;
    }

    createQRCodeElement(address, containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const qrUrl = this.generateQRCode(address);
        
        container.innerHTML = `
            <div class="qr-code-container">
                <img src="${qrUrl}" alt="Deposit Address QR Code" class="qr-code-image" />
                <p class="qr-code-address">${address}</p>
                <button class="btn btn-secondary btn-sm copy-address-btn" data-address="${address}">
                    📋 Copy Address
                </button>
            </div>
        `;

        // Add copy functionality
        const copyBtn = container.querySelector('.copy-address-btn');
        if (copyBtn) {
            copyBtn.addEventListener('click', () => this.copyToClipboard(address));
        }
    }

    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showCopySuccess();
        } catch (err) {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                document.execCommand('copy');
                this.showCopySuccess();
            } catch (err) {
                console.error('Failed to copy address:', err);
            }
            
            document.body.removeChild(textArea);
        }
    }

    showCopySuccess() {
        const message = document.createElement('div');
        message.className = 'copy-success-message';
        message.textContent = 'Address copied to clipboard!';
        message.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4CAF50;
            color: white;
            padding: 10px 15px;
            border-radius: 4px;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        `;
        
        document.body.appendChild(message);
        
        setTimeout(() => {
            document.body.removeChild(message);
        }, 3000);
    }
}

// Export for use in other modules
window.QRCodeGenerator = QRCodeGenerator;
