<?php
/**
 * Simple PHP Autoloader for TLS Frontend
 * 
 * This replaces the Composer PSR-4 autoloader to make the frontend
 * a standalone PHP application that can be hosted on any server.
 */

// Define the base directory
define('FRONTEND_ROOT', __DIR__);
define('FRONTEND_SRC', FRONTEND_ROOT . DIRECTORY_SEPARATOR . 'src');

/**
 * Simple class autoloader
 */
function tls_frontend_autoload($class) {
    // Convert namespace to file path
    $class = str_replace('\\', DIRECTORY_SEPARATOR, $class);
    
    // Remove the base namespace prefix if present
    $prefixes = [
        'Tojew' . DIRECTORY_SEPARATOR . 'Tls' . DIRECTORY_SEPARATOR . 'Frontend' . DIRECTORY_SEPARATOR,
        'Frontend' . DIRECTORY_SEPARATOR
    ];
    
    foreach ($prefixes as $prefix) {
        if (strpos($class, $prefix) === 0) {
            $class = substr($class, strlen($prefix));
            break;
        }
    }
    
    // Try to load the file from src directory
    $file = FRONTEND_SRC . DIRECTORY_SEPARATOR . $class . '.php';
    
    if (file_exists($file)) {
        require_once $file;
        return true;
    }
    
    return false;
}

// Register the autoloader
spl_autoload_register('tls_frontend_autoload');

// Include common functions and utilities
$common_files = [
    FRONTEND_ROOT . '/includes/functions.php'
];

foreach ($common_files as $file) {
    if (file_exists($file)) {
        require_once $file;
    }
}
