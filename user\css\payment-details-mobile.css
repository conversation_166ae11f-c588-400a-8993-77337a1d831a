/* ===============================================
   PAYMENT DETAILS MOBILE-FIRST RESPONSIVE DESIGN
   =============================================== */

/* Payment Details Container - Mobile First */
#paymentDetails {
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    margin: 20px 0;
    overflow: hidden;
    animation: slideInUp 0.5s ease-out;
}

#paymentDetails h3 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin: 0;
    padding: 20px;
    font-size: 1.3rem;
    font-weight: 600;
    text-align: center;
    border-bottom: none;
}

/* Payment Info Container */
.payment-info {
    padding: 0;
    background: transparent;
    border-radius: 0;
    border: none;
}

/* Deposit Summary - Enhanced Mobile Design */
.deposit-summary {
    background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
    margin: 0;
    padding: 20px;
    border-radius: 0;
    border: none;
    border-bottom: 1px solid #e9ecef;
    box-shadow: none;
}

.deposit-summary h4 {
    color: #2c3e50;
    margin-bottom: 16px;
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 2px solid #667eea;
    padding-bottom: 8px;
    text-align: center;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    border-bottom: 1px solid #f1f3f4;
    margin: 0;
}

.summary-item:last-child {
    border-bottom: none;
}

.summary-item .label {
    font-weight: 500;
    color: #6c757d;
    font-size: 0.95rem;
}

.summary-item .value {
    font-weight: 700;
    color: #2c3e50;
    font-size: 1.2rem;
}

.summary-item .currency {
    color: #667eea;
    font-weight: 700;
    margin-left: 6px;
    font-size: 1.1rem;
}

/* Payment Instructions - Mobile Optimized */
.payment-instructions {
    margin: 0;
    padding: 20px;
}

.payment-instructions h4 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.2rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-align: center;
}

.payment-instructions h4::before {
    content: "📋";
    font-size: 1.2rem;
}

/* Instruction Steps - Mobile First */
.instruction-step {
    margin-bottom: 24px;
    padding: 20px;
    background: #fff;
    border-radius: 12px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    position: relative;
}

.instruction-step:hover {
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

/* Special styling for warning step */
.instruction-step:first-child {
    border: 2px solid #dc3545;
    background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);
}

.step-content {
    width: 100%;
}

.step-content h5 {
    margin: 0 0 12px 0;
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
}

.step-content p {
    margin: 0;
    color: #6c757d;
    line-height: 1.6;
    font-size: 0.95rem;
}

/* Wallet Address Section - Mobile Optimized */
.wallet-address-section {
    margin-top: 16px;
}

.wallet-address-section label {
    display: block;
    margin-bottom: 12px;
    font-weight: 600;
    color: #495057;
    font-size: 1rem;
    text-align: center;
}

.address-display-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
    width: 100%;
}

#paymentAddress {
    background: #f8f9ff;
    padding: 16px;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    color: #495057;
    border: 2px solid #e9ecef;
    word-break: break-all;
    text-align: center;
    transition: all 0.3s ease;
    line-height: 1.4;
}

#paymentAddress:hover {
    background: #e9ecef;
    border-color: #667eea;
}

#copyPaymentBtn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    border: none;
    padding: 14px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    width: 100%;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

#copyPaymentBtn::before {
    content: "📋";
    font-size: 1.1rem;
}

#copyPaymentBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

#copyPaymentBtn:active {
    transform: translateY(0);
}

/* QR Code Section - Enhanced Mobile Design */
.qr-code-section {
    text-align: center;
    margin-top: 20px;
}

.qr-code-container {
    background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
    padding: 24px;
    border-radius: 12px;
    border: 2px solid #e9ecef;
    display: block;
    margin: 0 auto 16px auto;
    max-width: 100%;
    width: 100%;
}

.tron-qr-container {
    background: white;
    padding: 20px;
    border-radius: 12px;
    border: 2px solid #667eea;
    text-align: center;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.qr-header {
    margin-bottom: 16px;
}

.qr-header h5 {
    margin: 0 0 4px 0;
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
}

.qr-header small {
    color: #6c757d;
    font-size: 0.85rem;
}

.tron-qr-image {
    max-width: 200px !important;
    width: 100% !important;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin: 16px 0;
}

.qr-details {
    margin: 16px 0;
    padding: 16px;
    background: #f8f9ff;
    border-radius: 8px;
}

.qr-amount-large {
    font-size: 1.3rem;
    font-weight: 700;
    color: #667eea;
    margin-bottom: 8px;
}

.qr-address-small {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    color: #6c757d;
    margin-top: 8px;
}

.qr-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 16px;
}

.qr-actions .btn {
    padding: 12px 16px;
    border-radius: 8px;
    font-weight: 500;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    width: 100%;
}

.qr-help {
    color: #6c757d;
    font-size: 0.9rem;
    margin: 0;
    font-style: italic;
    text-align: center;
    background: #f8f9fa;
    padding: 12px;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

/* Warning Messages - Enhanced Mobile */
.deposit-warnings {
    background: linear-gradient(135deg, #fff3cd 0%, #ffffff 100%);
    border: 2px solid #ffc107;
    border-radius: 12px;
    padding: 20px;
    margin: 24px 20px;
}

.deposit-warnings h4 {
    color: #856404;
    margin-bottom: 16px;
    font-size: 1.1rem;
    font-weight: 600;
    text-align: center;
}

.deposit-warnings ul {
    margin: 0;
    padding-left: 20px;
    list-style: none;
}

.deposit-warnings li {
    margin-bottom: 12px;
    color: #856404;
    line-height: 1.6;
    position: relative;
    padding-left: 20px;
}

.deposit-warnings li::before {
    content: "⚠️";
    position: absolute;
    left: 0;
    top: 0;
}

.deposit-warnings strong {
    color: #744500;
}

/* Action Buttons - Mobile Optimized */
.deposit-actions {
    padding: 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.deposit-actions .btn {
    padding: 14px 20px;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    width: 100%;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

#newDepositBtn {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    color: white;
    border: none;
}

#newDepositBtn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

#viewTransactionsBtn {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
    border: none;
}

#viewTransactionsBtn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
}

/* Animation for payment details appearance */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design - Tablet and Above */
@media (min-width: 768px) {
    #paymentDetails {
        margin: 30px 0;
    }
    
    #paymentDetails h3 {
        font-size: 1.5rem;
        padding: 24px;
    }
    
    .payment-info {
        padding: 0;
    }
    
    .deposit-summary {
        padding: 24px;
    }
    
    .payment-instructions {
        padding: 24px;
    }
    
    .instruction-step {
        padding: 24px;
    }
    
    .address-display-container {
        flex-direction: row;
        align-items: stretch;
    }
    
    #paymentAddress {
        flex: 1;
        text-align: left;
    }
    
    #copyPaymentBtn {
        width: auto;
        min-width: 150px;
        flex-shrink: 0;
    }
      .qr-code-container {
        max-width: 400px;
        margin: 0 auto;
    }
    
    .qr-actions {
        flex-direction: row;
        justify-content: center;
    }
    
    .qr-actions .btn {
        width: auto;
        min-width: 140px;
    }
    
    .deposit-actions {
        flex-direction: row;
        justify-content: center;
    }
    
    .deposit-actions .btn {
        width: auto;
        min-width: 200px;
    }
    
    .deposit-warnings {
        margin: 24px;
    }
}

/* Large Screen Optimizations */
@media (min-width: 1024px) {
    #paymentDetails {
        max-width: 900px;
        margin: 30px auto;
    }
    
    .qr-code-container {
        max-width: 450px;
        margin: 0 auto;
    }
    
    .tron-qr-image {
        max-width: 280px !important;
    }
    
    .instruction-step {
        padding: 30px;
    }
}

/* Enhanced Mobile Touch Targets */
@media (max-width: 767px) {
    .instruction-step {
        margin-bottom: 20px;
        padding: 16px;
    }
    
    .step-content h5 {
        font-size: 1rem;
    }
    
    .step-content p {
        font-size: 0.9rem;
    }
    
    #paymentAddress {
        font-size: 12px;
        padding: 14px;
    }
    
    #copyPaymentBtn {
        padding: 16px;
        font-size: 1rem;
        min-height: 52px;
    }
    
    .deposit-warnings {
        margin: 16px;
        padding: 16px;
    }
    
    .deposit-warnings li {
        font-size: 0.9rem;
        margin-bottom: 10px;
    }
}

/* Dark mode support for payment details */
@media (prefers-color-scheme: dark) {
    #paymentDetails {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .deposit-summary {
        background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    }
    
    .instruction-step {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .step-content h5,
    .summary-item .value {
        color: #e2e8f0;
    }
    
    .step-content p,
    .summary-item .label {
        color: #a0aec0;
    }
    
    #paymentAddress {
        background: #1a202c;
        color: #e2e8f0;
        border-color: #4a5568;
    }
    
    .qr-code-container,
    .tron-qr-container {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .qr-details {
        background: #1a202c;
    }
}

/* Print styles for payment details */
@media print {
    #paymentDetails {
        box-shadow: none;
        border: 2px solid #000;
        margin: 0;
        page-break-inside: avoid;
    }
    
    .deposit-actions,
    .qr-actions {
        display: none;
    }
    
    #paymentAddress {
        background: white;
        border: 1px solid #000;
        font-size: 14px;
    }
    
    .instruction-step {
        border: 1px solid #000;
        margin-bottom: 20px;
    }
}
