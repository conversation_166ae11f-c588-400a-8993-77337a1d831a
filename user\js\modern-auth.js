// Modern Authentication JavaScript
class ModernAuth {
    constructor() {
        this.passwordStrength = {
            score: 0,
            requirements: {
                length: false,
                uppercase: false,
                lowercase: false,
                number: false,
                special: false
            }
        };
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.setupFormValidation();
        this.setupPasswordToggle();
        this.setupLiveValidation();
    }
    
    setupEventListeners() {
        // Login form
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', this.handleLogin.bind(this));
        }
        
        // Register form
        const registerForm = document.getElementById('registerForm');
        if (registerForm) {
            registerForm.addEventListener('submit', this.handleRegister.bind(this));
        }
        
        // Forgot password form
        const forgotPasswordForm = document.getElementById('forgotPasswordForm');
        if (forgotPasswordForm) {
            forgotPasswordForm.addEventListener('submit', this.handleForgotPassword.bind(this));
        }
    }
    
    setupFormValidation() {
        // Email validation
        const emailInputs = document.querySelectorAll('input[type="email"]');
        emailInputs.forEach(input => {
            input.addEventListener('blur', this.validateEmail.bind(this));
            input.addEventListener('input', this.clearValidation.bind(this));
        });
        
        // PIN validation
        const pinInput = document.getElementById('pin');
        if (pinInput) {
            pinInput.addEventListener('input', this.validatePin.bind(this));
            pinInput.addEventListener('keypress', this.restrictToNumbers.bind(this));
        }
        
        // Terms checkbox
        const termsCheckbox = document.getElementById('terms');
        if (termsCheckbox) {
            termsCheckbox.addEventListener('change', this.updateRegisterButton.bind(this));
        }
    }
    
    setupPasswordToggle() {
        // Password visibility toggle functionality is handled by the global togglePassword function
    }
    
    setupLiveValidation() {
        const passwordInput = document.getElementById('password');
        const confirmPasswordInput = document.getElementById('confirmPassword');
        
        if (passwordInput) {
            passwordInput.addEventListener('input', this.checkPasswordStrength.bind(this));
        }
        
        if (confirmPasswordInput) {
            confirmPasswordInput.addEventListener('input', this.validatePasswordMatch.bind(this));
        }
    }
    
    checkPasswordStrength(event) {
        const password = event.target.value;
        const strengthIndicator = document.getElementById('passwordStrength');
        
        if (!strengthIndicator) return;
        
        // Reset requirements
        this.passwordStrength.requirements = {
            length: password.length >= 8,
            uppercase: /[A-Z]/.test(password),
            lowercase: /[a-z]/.test(password),
            number: /\d/.test(password),
            special: /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)
        };
        
        // Calculate score
        this.passwordStrength.score = Object.values(this.passwordStrength.requirements)
            .filter(Boolean).length;
        
        // Update UI
        this.updatePasswordStrengthUI();
        this.updateRegisterButton();
        
        // Validate confirm password if it has a value
        const confirmPasswordInput = document.getElementById('confirmPassword');
        if (confirmPasswordInput && confirmPasswordInput.value) {
            this.validatePasswordMatch({ target: confirmPasswordInput });
        }
    }
    
    updatePasswordStrengthUI() {
        const strengthFill = document.querySelector('.strength-fill');
        const strengthText = document.querySelector('.strength-text');
        
        if (!strengthFill || !strengthText) return;
        
        // Remove existing classes
        strengthFill.className = 'strength-fill';
        
        // Update based on score
        let strengthLevel = '';
        let strengthMessage = '';
        
        switch (this.passwordStrength.score) {
            case 0:
            case 1:
                strengthLevel = 'weak';
                strengthMessage = 'Very weak password';
                break;
            case 2:
                strengthLevel = 'weak';
                strengthMessage = 'Weak password';
                break;
            case 3:
                strengthLevel = 'fair';
                strengthMessage = 'Fair password';
                break;
            case 4:
                strengthLevel = 'good';
                strengthMessage = 'Good password';
                break;
            case 5:
                strengthLevel = 'strong';
                strengthMessage = 'Strong password';
                break;
        }
        
        strengthFill.classList.add(strengthLevel);
        strengthText.textContent = strengthMessage;
        
        // Update requirement indicators
        Object.keys(this.passwordStrength.requirements).forEach(req => {
            const element = document.getElementById(`req-${req}`);
            if (element) {
                element.classList.toggle('met', this.passwordStrength.requirements[req]);
            }
        });
    }
    
    validatePasswordMatch(event) {
        const confirmPassword = event.target.value;
        const password = document.getElementById('password').value;
        const validationMessage = document.getElementById('confirmPasswordValidation');
        const validationIcon = document.getElementById('confirmPasswordIcon');
        
        if (!confirmPassword) {
            this.clearValidationMessage(validationMessage, validationIcon);
            this.updateRegisterButton();
            return;
        }
        
        const isMatch = password === confirmPassword;
        
        if (isMatch) {
            this.showValidationMessage(validationMessage, 'Passwords match', 'valid');
            this.showValidationIcon(validationIcon, 'fas fa-check', 'valid');
        } else {
            this.showValidationMessage(validationMessage, 'Passwords do not match', 'invalid');
            this.showValidationIcon(validationIcon, 'fas fa-times', 'invalid');
        }
        
        this.updateRegisterButton();
    }
    
    validateEmail(event) {
        const email = event.target.value;
        const validationMessage = event.target.parentNode.querySelector('.validation-message');
        
        if (!email) {
            this.clearValidationMessage(validationMessage);
            return;
        }
        
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        const isValid = emailRegex.test(email);
        
        if (isValid) {
            this.showValidationMessage(validationMessage, 'Valid email address', 'valid');
        } else {
            this.showValidationMessage(validationMessage, 'Please enter a valid email address', 'invalid');
        }
    }
    
    validatePin(event) {
        const pin = event.target.value;
        const validationMessage = document.getElementById('pinValidation');
        const validationIcon = document.getElementById('pinIcon');
        
        if (!pin) {
            this.clearValidationMessage(validationMessage, validationIcon);
            this.updateRegisterButton();
            return;
        }
        
        const isValid = /^\d{5}$/.test(pin);
        
        if (isValid) {
            this.showValidationMessage(validationMessage, 'Valid PIN', 'valid');
            this.showValidationIcon(validationIcon, 'fas fa-check', 'valid');
        } else {
            this.showValidationMessage(validationMessage, 'PIN must be exactly 5 digits', 'invalid');
            this.showValidationIcon(validationIcon, 'fas fa-times', 'invalid');
        }
        
        this.updateRegisterButton();
    }
    
    restrictToNumbers(event) {
        const char = String.fromCharCode(event.which);
        if (!/[0-9]/.test(char)) {
            event.preventDefault();
        }
    }
    
    updateRegisterButton() {
        const registerBtn = document.getElementById('registerBtn');
        if (!registerBtn) return;
        
        const password = document.getElementById('password')?.value || '';
        const confirmPassword = document.getElementById('confirmPassword')?.value || '';
        const pin = document.getElementById('pin')?.value || '';
        const email = document.getElementById('email')?.value || '';
        const terms = document.getElementById('terms')?.checked || false;
        
        const isPasswordStrong = this.passwordStrength.score >= 4;
        const isPasswordMatch = password === confirmPassword && confirmPassword !== '';
        const isPinValid = /^\d{5}$/.test(pin);
        const isEmailValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
        
        const isFormValid = isPasswordStrong && isPasswordMatch && isPinValid && isEmailValid && terms;
        
        registerBtn.disabled = !isFormValid;
    }
    
    clearValidation(event) {
        const validationMessage = event.target.parentNode.querySelector('.validation-message');
        this.clearValidationMessage(validationMessage);
    }
    
    showValidationMessage(element, message, type) {
        if (!element) return;
        element.textContent = message;
        element.className = `validation-message ${type}`;
    }
    
    showValidationIcon(element, iconClass, type) {
        if (!element) return;
        element.innerHTML = `<i class="${iconClass}"></i>`;
        element.className = `validation-icon ${type}`;
    }
    
    clearValidationMessage(messageElement, iconElement = null) {
        if (messageElement) {
            messageElement.textContent = '';
            messageElement.className = 'validation-message';
        }
        if (iconElement) {
            iconElement.innerHTML = '';
            iconElement.className = 'validation-icon';
        }
    }
    
    async handleLogin(event) {
        event.preventDefault();
        
        const form = event.target;
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        
        const email = formData.get('email');
        const password = formData.get('password');
        
        if (!email || !password) {
            this.showMessage('Please fill in all fields', 'error');
            return;
        }
        
        this.setButtonLoading(submitBtn, true);
        
        try {
            const result = await window.csrfManager.stateChangingRequest('login', {
                email: email,
                password: password
            });
            
            if (result.success) {
                this.showMessage('Login successful! Redirecting...', 'success');
                setTimeout(() => {
                    if (result.user && result.user.is_admin) {
                        window.location.href = 'admin.php';
                    } else {
                        window.location.href = 'user/dashboard.php';
                    }
                }, 1000);
            } else {
                this.showMessage(result.error || 'Login failed', 'error');
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showMessage('Connection error. Please try again.', 'error');
        } finally {
            this.setButtonLoading(submitBtn, false);
        }
    }
    
    async handleRegister(event) {
        event.preventDefault();

        const form = event.target;
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');

        const email = formData.get('email');
        const password = formData.get('password');
        const confirmPassword = formData.get('confirmPassword');
        const pin = formData.get('pin');
        const referredBy = formData.get('referredBy');
        const terms = formData.get('terms');

        // Validation
        if (!email || !password || !confirmPassword || !pin || !terms) {
            this.showMessage('Please fill in all fields and accept the terms', 'error');
            return;
        }

        if (password !== confirmPassword) {
            this.showMessage('Passwords do not match', 'error');
            return;
        }

        if (this.passwordStrength.score < 4) {
            this.showMessage('Please choose a stronger password', 'error');
            return;
        }

        if (!/^\d{5}$/.test(pin)) {
            this.showMessage('PIN must be exactly 5 digits', 'error');
            return;
        }

        this.setButtonLoading(submitBtn, true);

        try {
            const requestData = {
                email: email,
                password: password,
                pin: pin
            };

            // Add referral code if provided
            if (referredBy && referredBy.trim()) {
                requestData.referral = referredBy.trim();
            }

            const result = await window.csrfManager.stateChangingRequest('register', requestData);

            if (result.success) {
                this.showMessage('Registration successful! Redirecting...', 'success');
                setTimeout(() => {
                    if (result.user && result.user.is_admin) {
                        window.location.href = 'admin.php';
                    } else {
                        window.location.href = 'user/dashboard.php';
                    }
                }, 1000);
            } else {
                this.showMessage(result.error || 'Registration failed', 'error');
            }
        } catch (error) {
            console.error('Registration error:', error);
            this.showMessage('Connection error. Please try again.', 'error');
        } finally {
            this.setButtonLoading(submitBtn, false);
        }
    }
    
    async handleForgotPassword(event) {
        event.preventDefault();
        
        const form = event.target;
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        
        const email = formData.get('email');
        
        if (!email) {
            this.showMessage('Please enter your email address', 'error');
            return;
        }
        
        this.setButtonLoading(submitBtn, true);
        
        try {
            const result = await window.csrfManager.stateChangingRequest('forgot_password', {
                email: email
            });
            
            if (result.success) {
                this.showMessage('Password reset link sent to your email', 'success');
                this.closeForgotPassword();
            } else {
                this.showMessage(result.error || 'Failed to send reset link', 'error');
            }
        } catch (error) {
            console.error('Forgot password error:', error);
            this.showMessage('Connection error. Please try again.', 'error');
        } finally {
            this.setButtonLoading(submitBtn, false);
        }
    }
    
    setButtonLoading(button, loading) {
        if (loading) {
            button.classList.add('loading');
            button.disabled = true;
        } else {
            button.classList.remove('loading');
            button.disabled = false;
        }
    }
    
    showMessage(message, type = 'info') {
        const container = document.getElementById('message') || document.querySelector('.message-container');
        if (!container) return;
        
        const messageElement = document.createElement('div');
        messageElement.className = `message ${type}`;
        
        const icon = this.getMessageIcon(type);
        messageElement.innerHTML = `
            <i class="${icon}"></i>
            <span>${message}</span>
        `;
        
        container.appendChild(messageElement);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (messageElement.parentNode) {
                messageElement.style.animation = 'messageSlideOut 0.3s ease-in forwards';
                setTimeout(() => {
                    messageElement.remove();
                }, 300);
            }
        }, 5000);
    }
    
    getMessageIcon(type) {
        const icons = {
            success: 'fas fa-check-circle',
            error: 'fas fa-exclamation-circle',
            warning: 'fas fa-exclamation-triangle',
            info: 'fas fa-info-circle'
        };
        return icons[type] || icons.info;
    }
    
    closeForgotPassword() {
        const modal = document.getElementById('forgotPasswordModal');
        if (modal) {
            modal.classList.remove('active');
        }
    }
}

// Global functions for backward compatibility and modal handling
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const toggle = input.parentNode.querySelector('.password-toggle i');
    
    if (input.type === 'password') {
        input.type = 'text';
        toggle.className = 'fas fa-eye-slash';
    } else {
        input.type = 'password';
        toggle.className = 'fas fa-eye';
    }
}

function showForgotPassword() {
    const modal = document.getElementById('forgotPasswordModal');
    if (modal) {
        modal.classList.add('active');
        const emailInput = modal.querySelector('input[type="email"]');
        if (emailInput) {
            emailInput.focus();
        }
    }
}

function closeForgotPassword() {
    const modal = document.getElementById('forgotPasswordModal');
    if (modal) {
        modal.classList.remove('active');
    }
}

// Add slide out animation to CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes messageSlideOut {
        to {
            opacity: 0;
            transform: translateX(100%);
        }
    }
`;
document.head.appendChild(style);

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ModernAuth();
});

// Close modal when clicking outside
document.addEventListener('click', (event) => {
    const modal = document.getElementById('forgotPasswordModal');
    if (modal && event.target === modal) {
        closeForgotPassword();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', (event) => {
    if (event.key === 'Escape') {
        closeForgotPassword();
    }
});
